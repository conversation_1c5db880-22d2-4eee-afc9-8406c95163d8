# CNInfo 应用 Caddy 配置
cninfo-v2.bybing.me {
    # 静态文件服务
    root * ./frontend/dist
    file_server
    
    # API 反向代理到后端服务
    handle /api/* {
        reverse_proxy *************:5001 {
            # 添加健康检查
            health_uri /api/health
            health_interval 30s
            health_timeout 5s
            
            # 处理错误
            handle_response {
                @error status 5xx
                respond @error "Backend service unavailable" 503
            }
        }
    }
    
    # SPA 路由支持 - 所有非 API 请求都返回 index.html
    try_files {path} /index.html
    
    # 启用压缩
    encode {
        gzip
        zstd
    }
    
    # 静态资源缓存策略
    @static {
        path *.js *.css *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot
    }
    header @static {
        Cache-Control "public, max-age=31536000, immutable"
        Vary "Accept-Encoding"
    }
    
    # HTML 文件缓存策略
    @html {
        path *.html
    }
    header @html {
        Cache-Control "no-cache, no-store, must-revalidate"
        Pragma "no-cache"
        Expires "0"
    }
    
    # 安全头设置
    header {
        X-Content-Type-Options nosniff
        X-Frame-Options DENY
        X-XSS-Protection "1; mode=block"
        Referrer-Policy "strict-origin-when-cross-origin"
        Permissions-Policy "geolocation=(), microphone=(), camera=()"
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    }
    
    # 访问日志
    log {
        output file /var/log/caddy/cninfo-access.log {
            roll_size 100mb
            roll_keep 5
            roll_keep_for 720h
        }
        format json
        level INFO
    }
    
    # 错误日志
    log {
        output file /var/log/caddy/cninfo-error.log {
            roll_size 100mb
            roll_keep 5
            roll_keep_for 720h
        }
        format json
        level ERROR
    }
}

# HTTP 重定向到 HTTPS
http://cninfo-v2.bybing.me {
    redir https://cninfo-v2.bybing.me{uri} permanent
}
