# 年报分析工具 - 前端重构

## 🎨 设计系统

本项目采用现代化的 Next.js 风格设计，具有以下特点：

### 设计原则
- **简洁优雅**: 采用黑白配色方案，去除不必要的装饰
- **响应式设计**: 完美适配桌面端和移动端
- **一致性**: 统一的组件设计语言
- **可访问性**: 符合 WCAG 标准的无障碍设计

### 颜色系统
```css
/* 主色调 */
--primary: hsl(222.2 84% 4.9%)     /* 黑色 */
--secondary: hsl(210 40% 96%)      /* 浅灰 */
--background: hsl(0 0% 100%)       /* 白色 */
--foreground: hsl(222.2 84% 4.9%)  /* 深色文字 */

/* 状态颜色 */
--success: #22c55e    /* 绿色 */
--warning: #f59e0b    /* 黄色 */
--error: #ef4444      /* 红色 */
--info: #3b82f6       /* 蓝色 */
```

## 🧩 组件架构

### 基础组件 (UI Components)
位于 `components/ui/` 目录下：

- **Button**: 统一的按钮组件，支持多种变体和尺寸
- **Input/Textarea**: 表单输入组件，内置验证和错误状态
- **Card**: 卡片容器组件，用于内容分组
- **Badge**: 标签组件，用于状态显示
- **Loading**: 加载状态组件，包含骨架屏

### 业务组件
- **Sidebar**: 桌面端侧边栏导航
- **MobileNav**: 移动端导航组件
- **Header**: 页面头部组件
- **AnalysisForm**: 分析表单组件
- **ResultsDisplay**: 结果展示组件
- **TaskProgress**: 任务进度组件
- **AIAnalysis**: AI分析页面
- **DataImport**: 数据导入页面

## 📱 响应式设计

### 断点系统
```css
/* Tailwind CSS 断点 */
sm: 640px   /* 小屏幕 */
md: 768px   /* 中等屏幕 */
lg: 1024px  /* 大屏幕 */
xl: 1280px  /* 超大屏幕 */
```

### 布局适配
- **桌面端**: 侧边栏 + 主内容区域布局
- **移动端**: 顶部导航 + 底部标签栏布局
- **自适应网格**: 根据屏幕尺寸调整列数

## 🎭 动画系统

### 内置动画类
```css
.animate-in { animation-duration: 0.2s; }
.fade-in { animation-name: fadeIn; }
.slide-in-from-top { animation-name: slideInFromTop; }
.slide-in-from-bottom { animation-name: slideInFromBottom; }
.slide-in-from-left { animation-name: slideInFromLeft; }
.slide-in-from-right { animation-name: slideInFromRight; }
.zoom-in { animation-name: zoomIn; }
```

### 使用示例
```tsx
<Card className="animate-in fade-in slide-in-from-bottom">
  <CardContent>内容</CardContent>
</Card>
```

## 🛠️ 开发指南

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 组件开发规范

1. **使用 TypeScript**: 所有组件必须有类型定义
2. **Props 接口**: 为每个组件定义清晰的 Props 接口
3. **样式一致性**: 使用 Tailwind CSS 类名，避免内联样式
4. **可访问性**: 添加适当的 ARIA 标签和键盘导航支持

### 示例组件结构
```tsx
interface ComponentProps {
  title: string
  variant?: 'primary' | 'secondary'
  children: React.ReactNode
}

export function Component({ title, variant = 'primary', children }: ComponentProps) {
  return (
    <div className={cn('base-classes', variantClasses[variant])}>
      <h2>{title}</h2>
      {children}
    </div>
  )
}
```

## 📦 技术栈

- **React 18**: 最新的 React 版本
- **TypeScript**: 类型安全的 JavaScript
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Lucide React**: 现代化的图标库
- **React Hot Toast**: 优雅的通知组件
- **Framer Motion**: 流畅的动画库

## 🎯 性能优化

### 代码分割
- 使用动态导入 `lazy()` 和 `Suspense`
- 路由级别的代码分割

### 图片优化
- 使用 WebP 格式
- 响应式图片加载

### 缓存策略
- 静态资源缓存
- API 响应缓存

## 🔧 自定义配置

### Tailwind 配置
在 `tailwind.config.js` 中自定义：
- 颜色系统
- 字体系统
- 间距系统
- 动画效果

### CSS 变量
在 `globals.css` 中定义全局 CSS 变量：
```css
:root {
  --radius: 0.5rem;
  --font-sans: 'Inter', system-ui, sans-serif;
}
```

## 🚀 部署

### 环境变量
```env
NEXT_PUBLIC_API_URL=http://localhost:5000
```

### 构建命令
```bash
npm run build
npm start
```

## 📝 更新日志

### v2.0.0 (当前版本)
- ✨ 全新的 Next.js 风格设计
- 📱 完整的移动端适配
- 🎨 统一的设计系统
- ⚡ 性能优化和代码重构
- 🧩 模块化组件架构

### v1.0.0
- 基础功能实现
- 简单的 UI 界面
