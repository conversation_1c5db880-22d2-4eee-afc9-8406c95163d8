'use client'

import { useState, useEffect } from 'react'
import {
  BarChart3,
  Search,
  Bot,
  Upload
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface MobileNavProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

const navigation = [
  {
    id: 'analysis',
    name: '在线',
    icon: BarChart3,
  },
  {
    id: 'keyword',
    name: '本地',
    icon: Search,
  },
  {
    id: 'ai',
    name: 'AI',
    icon: Bo<PERSON>,
  },
  {
    id: 'import',
    name: '导入',
    icon: Upload,
  }
]

export function MobileNav({ activeTab, onTabChange }: MobileNavProps) {
  const [isVisible, setIsVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY



      // 滚动距离小于20px时始终显示
      if (currentScrollY < 20) {
        setIsVisible(true)
        setLastScrollY(currentScrollY)
        return
      }

      // 向下滚动时隐藏，向上滚动时显示
      if (currentScrollY > lastScrollY && currentScrollY > 50) {
        // 向下滚动且超过50px时隐藏
        setIsVisible(false)
      } else if (currentScrollY < lastScrollY) {
        // 向上滚动时显示
        setIsVisible(true)
      }

      setLastScrollY(currentScrollY)
    }

    // 添加滚动监听器
    window.addEventListener('scroll', handleScroll, { passive: true })

    // 清理函数
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [lastScrollY, isVisible])

  return (
    <>
      {/* 移动端悬浮椭圆底部导航栏 */}
      <div
        className="lg:hidden fixed bottom-6 left-1/2 z-40 safe-area-pb"
        style={{
          transform: `translateX(-50%) translateY(${isVisible ? '0px' : '100px'})`,
          opacity: isVisible ? 1 : 0,
          transition: 'transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.4s ease'
        }}
      >
        <div className="bg-white/40 backdrop-blur-md border border-white/50 rounded-full px-3 py-2 shadow-lg transform transition-transform duration-300 hover:scale-105">
          <div className="flex items-center justify-center space-x-4">
            {navigation.map((item) => {
              const Icon = item.icon
              const isActive = activeTab === item.id

              return (
                <button
                  key={item.id}
                  onClick={() => onTabChange(item.id)}
                  className={cn(
                    'flex flex-col items-center justify-center space-y-1 px-2 py-2 rounded-full transition-all duration-300 active:scale-90 min-w-[50px]',
                    isActive
                      ? 'text-white bg-black/80 shadow-md backdrop-blur-sm'
                      : 'text-gray-700 hover:text-gray-900 hover:bg-white/40'
                  )}
                >
                  <Icon className="w-5 h-5 flex-shrink-0" />
                  <span className="text-xs font-medium text-center leading-tight">
                    {item.name}
                  </span>
                </button>
              )
            })}
          </div>
        </div>
      </div>
    </>
  )
}
