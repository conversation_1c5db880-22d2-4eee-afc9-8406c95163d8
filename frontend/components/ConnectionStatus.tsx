'use client'

import { useState, useEffect, useRef } from 'react'
import {
  Wifi,
  WifiOff,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { Button } from './ui/Button'
import { Badge } from './ui/Badge'
import { apiMethods, getApiBaseUrl } from '@/lib/api'
import { cn } from '@/lib/utils'

interface ConnectionStatus {
  status: 'checking' | 'connected' | 'disconnected' | 'error'
  message: string
  latency?: number
  timestamp?: Date
}

interface ApiConfig {
  baseUrl: string
  apiPrefix: string
  timeout: number
  healthEndpoint: string
}

export function ConnectionStatus() {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    status: 'disconnected',
    message: '未检测'
  })
  const [isChecking, setIsChecking] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const [showDetails, setShowDetails] = useState(false)
  const [apiConfig, setApiConfig] = useState<ApiConfig>({
    baseUrl: '',
    apiPrefix: '/api',
    timeout: 30000,
    healthEndpoint: '/health'
  })
  const containerRef = useRef<HTMLDivElement>(null)

  // 点击外部区域关闭弹窗和ESC键关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsExpanded(false)
      }
    }

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsExpanded(false)
      }
    }

    if (isExpanded) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscapeKey)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
        document.removeEventListener('keydown', handleEscapeKey)
      }
    }
  }, [isExpanded])

  // 初始化API配置
  useEffect(() => {
    const initApiConfig = () => {
      const baseUrl = getApiBaseUrl()
      const fullBaseUrl = baseUrl || window.location.origin

      setApiConfig({
        baseUrl: fullBaseUrl,
        apiPrefix: '/api',
        timeout: 30000,
        healthEndpoint: '/health'
      })
    }

    initApiConfig()
  }, [])

  // 获取环境信息
  const getEnvironmentInfo = () => {
    const isDev = import.meta.env.DEV
    const mode = import.meta.env.MODE
    const customApiUrl = import.meta.env.VITE_API_URL

    return {
      isDev,
      mode,
      customApiUrl,
      currentOrigin: window.location.origin,
      currentHost: window.location.host,
      protocol: window.location.protocol
    }
  }

  // 自动检测连接状态
  useEffect(() => {
    if (apiConfig.baseUrl) {
      checkConnection()
      // 每30秒自动检测一次
      const interval = setInterval(checkConnection, 30000)
      return () => clearInterval(interval)
    }
  }, [apiConfig.baseUrl])

  const checkConnection = async () => {
    setIsChecking(true)
    setConnectionStatus(prev => ({
      ...prev,
      status: 'checking',
      message: '正在检测连接...'
    }))

    const startTime = Date.now()

    try {
      const response = await apiMethods.healthCheck()
      const latency = Date.now() - startTime

      if (response.status === 200) {
        const data = response.data
        setConnectionStatus({
          status: 'connected',
          message: data.data?.message || '后端连接正常',
          latency,
          timestamp: new Date()
        })
      } else {
        setConnectionStatus({
          status: 'error',
          message: `HTTP ${response.status}: ${response.statusText}`,
          timestamp: new Date()
        })
      }
    } catch (error: any) {
      const latency = Date.now() - startTime
      let message = '连接失败'
      
      if (error.code === 'ECONNREFUSED') {
        message = '后端服务器未启动'
      } else if (error.code === 'ECONNABORTED') {
        message = '连接超时'
      } else if (error.response?.status === 404) {
        message = 'API接口不存在'
      } else if (error.response?.status === 500) {
        message = '服务器内部错误'
      } else if (error.message) {
        message = error.message
      }

      setConnectionStatus({
        status: 'disconnected',
        message,
        latency,
        timestamp: new Date()
      })
    } finally {
      setIsChecking(false)
    }
  }

  const getStatusIcon = () => {
    switch (connectionStatus.status) {
      case 'checking':
        return <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'disconnected':
        return <WifiOff className="w-4 h-4 text-red-600" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />
      default:
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />
    }
  }

  const getStatusBadge = () => {
    switch (connectionStatus.status) {
      case 'checking':
        return <Badge variant="info" size="sm">检测中</Badge>
      case 'connected':
        return <Badge variant="success" size="sm">已连接</Badge>
      case 'disconnected':
        return <Badge variant="error" size="sm">未连接</Badge>
      case 'error':
        return <Badge variant="error" size="sm">错误</Badge>
      default:
        return <Badge variant="warning" size="sm">未知</Badge>
    }
  }

  const getStatusColor = () => {
    switch (connectionStatus.status) {
      case 'connected':
        return 'text-green-600'
      case 'disconnected':
      case 'error':
        return 'text-red-600'
      case 'checking':
        return 'text-blue-600'
      default:
        return 'text-yellow-600'
    }
  }

  return (
    <div className="relative" ref={containerRef}>
      {/* 状态指示器 */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={cn(
          'flex items-center space-x-2 px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200',
          connectionStatus.status === 'connected' && 'bg-green-50 text-green-700 hover:bg-green-100',
          connectionStatus.status === 'disconnected' && 'bg-red-50 text-red-700 hover:bg-red-100',
          connectionStatus.status === 'error' && 'bg-red-50 text-red-700 hover:bg-red-100',
          connectionStatus.status === 'checking' && 'bg-blue-50 text-blue-700 hover:bg-blue-100'
        )}
      >
        {getStatusIcon()}
        <span className="hidden sm:inline">
          {connectionStatus.status === 'connected' && '已连接'}
          {connectionStatus.status === 'disconnected' && '未连接'}
          {connectionStatus.status === 'error' && '错误'}
          {connectionStatus.status === 'checking' && '检测中'}
        </span>
        {isExpanded ? (
          <ChevronUp className="w-3 h-3" />
        ) : (
          <ChevronDown className="w-3 h-3" />
        )}
      </button>

      {/* 详细信息浮动窗口 */}
      {isExpanded && (
        <div className="absolute top-full right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50 animate-in fade-in slide-in-from-top-2 duration-200">
          <div className="p-4 space-y-4">
            {/* 状态头部 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {getStatusIcon()}
                <span className="font-medium text-gray-900">后端连接状态</span>
              </div>
              {getStatusBadge()}
            </div>

            {/* 状态消息 */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className={cn('text-sm font-medium', getStatusColor())}>
                {connectionStatus.message}
              </p>
            </div>

            {/* 详细信息 */}
            {connectionStatus.latency !== undefined && (
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="font-medium text-gray-700">响应时间</p>
                  <p className="text-gray-900">{connectionStatus.latency}ms</p>
                </div>
                {connectionStatus.timestamp && (
                  <div>
                    <p className="font-medium text-gray-700">检测时间</p>
                    <p className="text-gray-600">
                      {connectionStatus.timestamp.toLocaleTimeString()}
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* 连接信息 */}
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium text-gray-700">连接信息</h4>
                <button
                  onClick={() => setShowDetails(!showDetails)}
                  className="text-xs text-gray-500 hover:text-gray-700 flex items-center"
                >
                  {showDetails ? '收起' : '详情'}
                  {showDetails ? (
                    <ChevronUp className="w-3 h-3 ml-1" />
                  ) : (
                    <ChevronDown className="w-3 h-3 ml-1" />
                  )}
                </button>
              </div>

              {/* 基本信息 - 始终显示 */}
              <div className="space-y-1 text-xs text-gray-600">
                <div className="flex justify-between">
                  <span>API地址:</span>
                  <span className="font-mono text-right break-all">
                    {apiConfig.baseUrl}{apiConfig.apiPrefix}
                  </span>
                </div>
              </div>

              {/* 详细信息 - 可折叠 */}
              {showDetails && (
                <div className="mt-2 pt-2 border-t border-gray-200 space-y-1 text-xs text-gray-600">
                  <div className="flex justify-between">
                    <span>后端地址:</span>
                    <span className="font-mono text-right break-all">
                      {apiConfig.baseUrl || '未配置'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>API前缀:</span>
                    <span className="font-mono">{apiConfig.apiPrefix}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>健康检查:</span>
                    <span className="font-mono text-right break-all">
                      {apiConfig.baseUrl}{apiConfig.apiPrefix}{apiConfig.healthEndpoint}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>超时时间:</span>
                    <span>{(apiConfig.timeout / 1000).toFixed(0)}秒</span>
                  </div>
                  {(() => {
                    const envInfo = getEnvironmentInfo()
                    return (
                      <>
                        <div className="flex justify-between">
                          <span>运行模式:</span>
                          <span className="font-mono">
                            {envInfo.mode} {envInfo.isDev ? '(开发)' : '(生产)'}
                          </span>
                        </div>
                        {envInfo.customApiUrl && (
                          <div className="flex justify-between">
                            <span>自定义API:</span>
                            <span className="font-mono text-right break-all">
                              {envInfo.customApiUrl}
                            </span>
                          </div>
                        )}
                      </>
                    )
                  })()}
                </div>
              )}
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center justify-between pt-2 border-t border-gray-200">
              <Button
                variant="ghost"
                size="sm"
                onClick={checkConnection}
                disabled={isChecking}
                loading={isChecking}
              >
                <RefreshCw className="w-4 h-4 mr-1" />
                重新检测
              </Button>

              {connectionStatus.status === 'disconnected' && (
                <div className="text-xs text-red-600">
                  请确保后端服务器已启动
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
