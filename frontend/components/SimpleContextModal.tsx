'use client'

import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { X, FileText, Search, ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from './ui/Button'
import { Badge } from './ui/Badge'
import { Loading } from './ui/Loading'
import { apiMethods } from '@/lib/api'
import toast from 'react-hot-toast'

interface SimpleContextModalProps {
  isOpen: boolean
  onClose: () => void
  item: {
    analysis_id: string
    keyword: string
    company_name: string
    stock_code: string
    file_name: string
    count: number
  } | null
}

export function SimpleContextModal({ isOpen, onClose, item }: SimpleContextModalProps) {
  const [contextData, setContextData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [snippetsPerPage] = useState(5) // 每页显示5个片段

  useEffect(() => {
    if (isOpen && item) {
      setCurrentPage(1) // 重置分页
      fetchContext()
    }
  }, [isOpen, item])

  const fetchContext = async () => {
    if (!item) return

    setLoading(true)
    try {
      // 获取上下文
      
      const response = await apiMethods.getKeywordContext({
        analysis_id: item.analysis_id,
        keyword: item.keyword,
        context_length: 200,
        stock_code_filter: item.stock_code,
        file_name_filter: item.file_name
      })

      // 处理上下文响应

      if (response.data.success) {
        setContextData(response.data.data)
      } else {
        toast.error(response.data.message || '获取上下文失败')
      }
    } catch (error: any) {
      toast.error('获取上下文失败: ' + (error.response?.data?.message || error.message))
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setContextData(null)
    setCurrentPage(1)
    onClose()
  }

  if (!isOpen || !item) return null

  const contexts = contextData?.contexts || []

  // 收集所有片段用于分页
  const allSnippets: Array<{snippet: string, fileName: string, fileIndex: number}> = []
  contexts.forEach((context: any, fileIndex: number) => {
    if (context.snippets && Array.isArray(context.snippets)) {
      context.snippets.forEach((snippet: string) => {
        allSnippets.push({
          snippet,
          fileName: context.file_name || '未知文件',
          fileIndex
        })
      })
    }
  })

  // 分页计算
  const totalSnippets = allSnippets.length
  const totalPages = Math.ceil(totalSnippets / snippetsPerPage)
  const startIndex = (currentPage - 1) * snippetsPerPage
  const endIndex = startIndex + snippetsPerPage
  const currentSnippets = allSnippets.slice(startIndex, endIndex)

  const modalContent = (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4 sm:p-6" style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}>
      {/* 背景遮罩 */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm transition-opacity"
        onClick={handleClose}
      />

      {/* 模态框 */}
      <div
        className="relative bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col z-10 border border-gray-200/50 overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
          {/* 头部 */}
          <div className="flex items-start justify-between p-6 border-b border-gray-100">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-3">
                <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Search className="w-5 h-5 text-blue-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-xl font-semibold text-gray-900 truncate">
                    关键词上下文
                  </h3>
                  <p className="text-sm text-gray-500 mt-1">
                    查看 "{item.keyword}" 的详细上下文信息
                  </p>
                </div>
              </div>

              <div className="flex flex-wrap items-center gap-2 sm:gap-3">
                <div className="flex items-center space-x-2 px-3 py-1.5 bg-gray-50 rounded-lg">
                  <span className="text-xs font-medium text-gray-500">公司</span>
                  <span className="text-sm font-medium text-gray-900 truncate max-w-[120px] sm:max-w-none">{item.company_name}</span>
                </div>
                <div className="flex items-center space-x-2 px-3 py-1.5 bg-gray-50 rounded-lg">
                  <FileText className="w-3.5 h-3.5 text-gray-400" />
                  <span className="text-sm text-gray-600 truncate max-w-[150px] sm:max-w-[200px]" title={item.file_name}>
                    {item.file_name}
                  </span>
                </div>
                <div className="flex items-center space-x-2 px-3 py-1.5 bg-green-50 rounded-lg">
                  <span className="text-xs font-medium text-green-600">出现次数</span>
                  <span className="text-sm font-semibold text-green-700">{item.count}</span>
                </div>
              </div>
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="flex-shrink-0 ml-4 h-8 w-8 p-0 hover:bg-gray-100 rounded-lg"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center py-16">
                <div className="text-center">
                  <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                  <p className="text-sm text-gray-600">正在获取上下文...</p>
                </div>
              </div>
            ) : contexts.length > 0 ? (
              <div className="p-6 space-y-6">
                {/* 统计信息 */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-xl p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <FileText className="w-4 h-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-blue-900">
                          找到 {contexts.length} 个相关文件
                        </p>
                        <p className="text-sm text-blue-700">
                          总计 {totalSnippets} 个上下文片段
                        </p>
                      </div>
                    </div>
                    {totalPages > 1 && (
                      <div className="text-sm font-medium text-blue-700 bg-blue-100 px-3 py-1 rounded-lg">
                        第 {currentPage} / {totalPages} 页
                      </div>
                    )}
                  </div>
                </div>

                {/* 分页上下文列表 */}
                <div className="space-y-4">
                  {currentSnippets.map((item_snippet, index) => (
                    <div key={index} className="group bg-white border border-gray-200 rounded-xl overflow-hidden hover:shadow-md transition-all duration-200">
                      <div className="bg-gradient-to-r from-gray-50 to-gray-100/50 px-5 py-3 border-b border-gray-100">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-6 h-6 bg-gray-200 rounded-lg flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                              <FileText className="w-3.5 h-3.5 text-gray-600 group-hover:text-blue-600" />
                            </div>
                            <span className="font-medium text-gray-900 text-sm">
                              {item_snippet.fileName}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-gray-500">片段</span>
                            <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-700 text-xs font-semibold rounded-full">
                              {startIndex + index + 1}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="p-5">
                        <div className="relative">
                          <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-yellow-400 to-orange-400 rounded-full"></div>
                          <div className="pl-4 pr-2">
                            <div
                              className="text-sm text-gray-700 leading-relaxed"
                              dangerouslySetInnerHTML={{
                                __html: item_snippet.snippet.replace(
                                  new RegExp(`(${item.keyword})`, 'gi'),
                                  '<mark class="bg-yellow-200 text-yellow-900 px-1.5 py-0.5 rounded-md font-medium shadow-sm">$1</mark>'
                                )
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* 分页控件 */}
                {totalPages > 1 && (
                  <div className="pt-6 border-t border-gray-100">
                    {/* 移动端分页 */}
                    <div className="sm:hidden">
                      <div className="flex items-center justify-between mb-3">
                        <div className="text-xs text-gray-600">
                          {startIndex + 1}-{Math.min(endIndex, totalSnippets)} / {totalSnippets}
                        </div>
                        <div className="text-xs text-gray-600">
                          第 {currentPage} / {totalPages} 页
                        </div>
                      </div>
                      <div className="flex items-center justify-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                          disabled={currentPage === 1}
                          className="h-8 px-3 text-xs"
                        >
                          <ChevronLeft className="w-3 h-3 mr-1" />
                          上一页
                        </Button>

                        <div className="flex items-center space-x-1">
                          {Array.from({ length: Math.min(3, totalPages) }, (_, i) => {
                            let pageNum
                            if (totalPages <= 3) {
                              pageNum = i + 1
                            } else if (currentPage <= 2) {
                              pageNum = i + 1
                            } else if (currentPage >= totalPages - 1) {
                              pageNum = totalPages - 2 + i
                            } else {
                              pageNum = currentPage - 1 + i
                            }

                            return (
                              <Button
                                key={pageNum}
                                variant={currentPage === pageNum ? "default" : "ghost"}
                                size="sm"
                                onClick={() => setCurrentPage(pageNum)}
                                className={`w-8 h-8 p-0 text-xs ${
                                  currentPage === pageNum
                                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                                    : 'hover:bg-gray-100'
                                }`}
                              >
                                {pageNum}
                              </Button>
                            )
                          })}
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                          disabled={currentPage === totalPages}
                          className="h-8 px-3 text-xs"
                        >
                          下一页
                          <ChevronRight className="w-3 h-3 ml-1" />
                        </Button>
                      </div>
                    </div>

                    {/* 桌面端分页 */}
                    <div className="hidden sm:flex items-center justify-between">
                      <div className="text-sm text-gray-600">
                        显示第 {startIndex + 1} - {Math.min(endIndex, totalSnippets)} 条，共 {totalSnippets} 条片段
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                          disabled={currentPage === 1}
                          className="h-9 px-3 hover:bg-gray-100 disabled:opacity-50"
                        >
                          <ChevronLeft className="w-4 h-4 mr-1" />
                          上一页
                        </Button>

                        <div className="flex items-center space-x-1">
                          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                            let pageNum
                            if (totalPages <= 5) {
                              pageNum = i + 1
                            } else if (currentPage <= 3) {
                              pageNum = i + 1
                            } else if (currentPage >= totalPages - 2) {
                              pageNum = totalPages - 4 + i
                            } else {
                              pageNum = currentPage - 2 + i
                            }

                            return (
                              <Button
                                key={pageNum}
                                variant={currentPage === pageNum ? "default" : "ghost"}
                                size="sm"
                                onClick={() => setCurrentPage(pageNum)}
                                className={`w-9 h-9 p-0 ${
                                  currentPage === pageNum
                                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                                    : 'hover:bg-gray-100'
                                }`}
                              >
                                {pageNum}
                              </Button>
                            )
                          })}
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                          disabled={currentPage === totalPages}
                          className="h-9 px-3 hover:bg-gray-100 disabled:opacity-50"
                        >
                          下一页
                          <ChevronRight className="w-4 h-4 ml-1" />
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center justify-center py-16">
                <div className="text-center max-w-md">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Search className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    未找到相关上下文
                  </h3>
                  <p className="text-gray-500 text-sm leading-relaxed">
                    关键词 "<span className="font-medium text-gray-700">{item.keyword}</span>" 在该文件中可能没有具体的上下文信息，或者数据正在处理中。
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* 底部 */}
          <div className="flex items-center justify-between p-6 border-t border-gray-100 bg-gray-50/50">
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <span>分析ID:</span>
              <code className="px-2 py-1 bg-gray-200 rounded font-mono text-gray-700">
                {item.analysis_id}
              </code>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="ghost"
                onClick={handleClose}
                className="h-9 px-4 hover:bg-gray-200"
              >
                关闭
              </Button>
            </div>
          </div>
        </div>
    </div>
  )

  // 使用 Portal 将弹窗渲染到 body 元素下，确保不受父容器限制
  return typeof window !== 'undefined' ? createPortal(modalContent, document.body) : null
}
