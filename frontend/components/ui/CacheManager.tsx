'use client'

import { useState, useEffect } from 'react'
import { 
  Database, 
  Trash2, 
  RefreshCw, 
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { Card, CardHeader, CardTitle, CardContent } from './Card'
import { Button } from './Button'
import { Badge } from './Badge'
import { cacheUtils } from '@/lib/cache'

interface CacheStats {
  size: number
  keys: string[]
  hasOnlineAnalysis: boolean
  hasLocalAnalysis: boolean
  hasAIAnalysis: boolean
  hasModelConfig: boolean
  hasAIHistory: boolean
  aiHistoryCount: number
}

export function CacheManager() {
  const [cacheStats, setCacheStats] = useState<CacheStats>({
    size: 0,
    keys: [],
    hasOnlineAnalysis: false,
    hasLocalAnalysis: false,
    hasAIAnalysis: false,
    hasModelConfig: false,
    hasAIHistory: false,
    aiHistoryCount: 0
  })
  const [isClearing, setIsClearing] = useState(false)

  const refreshStats = () => {
    const stats = cacheUtils.getCacheStats()
    const aiHistory = cacheUtils.getAIAnalysisHistory()
    setCacheStats({
      ...stats,
      hasAIHistory: aiHistory.length > 0,
      aiHistoryCount: aiHistory.length
    })
  }

  useEffect(() => {
    refreshStats()
  }, [])

  const handleClearCache = async () => {
    if (!confirm('确定要清空所有缓存数据吗？这将删除所有已保存的分析参数。')) {
      return
    }

    setIsClearing(true)
    try {
      cacheUtils.clearAnalysisCache()
      refreshStats()
      alert('缓存已清空')
    } catch (error) {
      alert('清空缓存失败')
    } finally {
      setIsClearing(false)
    }
  }

  const formatSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const cacheItems = [
    {
      key: 'online_analysis',
      name: '在线分析参数',
      hasData: cacheStats.hasOnlineAnalysis,
      description: '股票代码、关键词、时间范围等'
    },
    {
      key: 'local_analysis',
      name: '本地分析参数',
      hasData: cacheStats.hasLocalAnalysis,
      description: '本地关键词分析的配置参数'
    },
    {
      key: 'ai_analysis',
      name: 'AI分析参数',
      hasData: cacheStats.hasAIAnalysis,
      description: 'AI分析的股票代码、关键词、提示词等'
    },
    {
      key: 'model_config',
      name: 'AI模型配置',
      hasData: cacheStats.hasModelConfig,
      description: 'OpenAI API配置信息'
    },
    {
      key: 'ai_history',
      name: 'AI分析历史',
      hasData: cacheStats.hasAIHistory,
      description: `AI分析历史记录 (${cacheStats.aiHistoryCount}条)`
    }
  ]

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Database className="w-5 h-5" />
            <span>缓存管理</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={refreshStats}
            >
              <RefreshCw className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearCache}
              disabled={isClearing}
              loading={isClearing}
            >
              <Trash2 className="w-4 h-4 mr-1" />
              清空缓存
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 缓存统计 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{formatSize(cacheStats.size)}</div>
            <div className="text-sm text-gray-500">缓存大小</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{cacheStats.keys.length}</div>
            <div className="text-sm text-gray-500">缓存项目</div>
          </div>
        </div>

        {/* 缓存项目列表 */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">缓存项目</h4>
          {cacheItems.map((item) => (
            <div key={item.key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                {item.hasData ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <XCircle className="w-5 h-5 text-gray-400" />
                )}
                <div>
                  <div className="font-medium text-gray-900">{item.name}</div>
                  <div className="text-sm text-gray-500">{item.description}</div>
                </div>
              </div>
              <Badge variant={item.hasData ? "success" : "default"} size="sm">
                {item.hasData ? "已缓存" : "无数据"}
              </Badge>
            </div>
          ))}
        </div>

        {/* 说明信息 */}
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start space-x-2">
            <Info className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">缓存说明：</p>
              <ul className="space-y-1 text-xs">
                <li>• 缓存会自动保存您的输入参数，方便下次使用</li>
                <li>• 缓存数据存储在浏览器本地，不会上传到服务器</li>
                <li>• 缓存有效期为7天，过期后会自动清理</li>
                <li>• 清空缓存后，所有保存的参数将被删除</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
