"""
响应工具模块
"""
from flask import jsonify
from typing import Any, Dict, Optional


def success_response(data: Any = None, message: str = None) -> Dict:
    """成功响应"""
    response = {
        'success': True
    }
    
    if data is not None:
        response['data'] = data
    
    if message:
        response['message'] = message
    
    return jsonify(response)


def error_response(message: str, code: int = 400, data: Any = None) -> Dict:
    """错误响应"""
    response = {
        'success': False,
        'message': message
    }
    
    if data is not None:
        response['data'] = data
    
    return jsonify(response), code


def paginated_response(data: list, page: int, page_size: int, total: int, message: str = None) -> Dict:
    """分页响应"""
    response = {
        'success': True,
        'data': data,
        'pagination': {
            'page': page,
            'page_size': page_size,
            'total': total,
            'total_pages': (total + page_size - 1) // page_size,
            'has_next': page * page_size < total,
            'has_prev': page > 1
        }
    }
    
    if message:
        response['message'] = message
    
    return jsonify(response)
