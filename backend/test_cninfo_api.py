#!/usr/bin/env python3
"""
测试巨潮网API接口是否正常工作
"""
import requests
import json
import random
import time
from logger_utils import log_info, log_debug, log_warning, log_error, log_success

def test_cninfo_api():
    """测试巨潮网API接口"""
    
    # 用户代理列表
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ]
    
    # 请求头
    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Accept-Encoding': 'gzip, deflate',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Host': 'www.cninfo.com.cn',
        'Origin': 'http://www.cninfo.com.cn',
        'Referer': 'http://www.cninfo.com.cn/new/commonUrl?url=disclosure/list/notice',
        'X-Requested-With': 'XMLHttpRequest',
        'User-Agent': random.choice(user_agents)
    }
    
    # API URLs
    orgid_url = 'http://www.cninfo.com.cn/new/data/szse_stock.json'
    query_url = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
    
    log_info("开始测试巨潮网API接口", "APITest")
    
    # 1. 测试获取股票列表
    log_info("测试1: 获取股票列表", "APITest")
    try:
        response = requests.get(orgid_url, headers=headers, timeout=10)
        log_info(f"股票列表API响应状态: {response.status_code}", "APITest")
        
        if response.status_code == 200:
            data = response.json()
            stock_list = data.get('stockList', [])
            log_success(f"成功获取股票列表，共 {len(stock_list)} 只股票", "APITest")
            
            # 找一个测试用的股票代码
            test_stock = None
            for stock in stock_list[:10]:  # 检查前10个
                if stock.get('code') and stock.get('orgId'):
                    test_stock = stock
                    break
            
            if test_stock:
                log_info(f"选择测试股票: {test_stock['code']} - {test_stock.get('zwjc', 'Unknown')}", "APITest")
                
                # 2. 测试搜索年报
                log_info("测试2: 搜索年报公告", "APITest")
                test_search_announcements(query_url, headers, test_stock['code'], test_stock['orgId'])
            else:
                log_error("未找到可用的测试股票", "APITest")
        else:
            log_error(f"获取股票列表失败: HTTP {response.status_code}", "APITest")
            log_error(f"响应内容: {response.text[:500]}", "APITest")
            
    except Exception as e:
        log_error(f"获取股票列表异常: {e}", "APITest")

def test_search_announcements(query_url, headers, stock_code, org_id):
    """测试搜索年报公告"""
    
    # 深市查询参数
    szse_query = {
        'pageNum': 1,
        'pageSize': 10,  # 减少数量，只测试
        'tabName': 'fulltext',
        'column': 'szse',
        'stock': f'{stock_code},{org_id}',
        'searchkey': '',
        'secid': '',
        'plate': 'sz',
        'category': 'category_ndbg_szsh',
        'trade': '',
        'seDate': '2024-01-01~2025-12-31',
        'sortName': '',
        'sortType': '',
        'isHLtitle': 'true'
    }
    
    # 沪市查询参数
    sse_query = szse_query.copy()
    sse_query.update({
        'column': 'sse',
        'plate': 'sh'
    })
    
    # 测试深市查询
    log_info(f"测试深市查询: {stock_code}", "APITest")
    try:
        response = requests.post(query_url, data=szse_query, headers=headers, timeout=10)
        log_info(f"深市查询响应状态: {response.status_code}", "APITest")
        
        if response.status_code == 200:
            data = response.json()
            announcements = data.get('announcements', [])
            log_info(f"深市找到 {len(announcements)} 条公告", "APITest")
            
            # 显示前几个公告标题
            for i, ann in enumerate(announcements[:3]):
                title = ann.get('announcementTitle', 'Unknown')
                date = ann.get('announcementTime', 'Unknown')
                log_info(f"  公告{i+1}: {title} ({date})", "APITest")
        else:
            log_error(f"深市查询失败: HTTP {response.status_code}", "APITest")
            log_error(f"响应内容: {response.text[:500]}", "APITest")
            
    except Exception as e:
        log_error(f"深市查询异常: {e}", "APITest")
    
    time.sleep(1)  # 避免请求过快
    
    # 测试沪市查询
    log_info(f"测试沪市查询: {stock_code}", "APITest")
    try:
        response = requests.post(query_url, data=sse_query, headers=headers, timeout=10)
        log_info(f"沪市查询响应状态: {response.status_code}", "APITest")
        
        if response.status_code == 200:
            data = response.json()
            announcements = data.get('announcements', [])
            log_info(f"沪市找到 {len(announcements)} 条公告", "APITest")
            
            # 显示前几个公告标题
            for i, ann in enumerate(announcements[:3]):
                title = ann.get('announcementTitle', 'Unknown')
                date = ann.get('announcementTime', 'Unknown')
                log_info(f"  公告{i+1}: {title} ({date})", "APITest")
        else:
            log_error(f"沪市查询失败: HTTP {response.status_code}", "APITest")
            log_error(f"响应内容: {response.text[:500]}", "APITest")
            
    except Exception as e:
        log_error(f"沪市查询异常: {e}", "APITest")

def test_specific_stock():
    """测试特定股票代码"""
    log_info("测试3: 测试特定股票代码", "APITest")
    
    # 测试一些知名股票
    test_stocks = [
        "000001",  # 平安银行
        "000002",  # 万科A
        "600000",  # 浦发银行
        "600036",  # 招商银行
    ]
    
    for stock_code in test_stocks:
        log_info(f"测试股票代码: {stock_code}", "APITest")
        # 这里可以调用实际的API测试
        # 为了简化，只是记录日志
        time.sleep(0.5)

if __name__ == "__main__":
    log_info("=" * 50, "APITest")
    log_info("巨潮网API接口测试开始", "APITest")
    log_info("=" * 50, "APITest")
    
    test_cninfo_api()
    test_specific_stock()
    
    log_info("=" * 50, "APITest")
    log_info("巨潮网API接口测试完成", "APITest")
    log_info("=" * 50, "APITest")
