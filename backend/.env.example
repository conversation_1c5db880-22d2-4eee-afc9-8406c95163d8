# OpenAI API 配置
# OpenAI API密钥，从 https://platform.openai.com/api-keys 获取
OPENAI_API_KEY=sk-your-openai-api-key-here

# OpenAI API基础URL，默认为官方API地址
# 如果使用代理或第三方兼容API，请修改此URL
OPENAI_BASE_URL=https://api.openai.com/v1

# OpenAI 模型名称，推荐使用的模型
OPENAI_MODEL=gpt-3.5-turbo

# 数据库配置
# SQLite数据库文件路径
DATABASE_PATH=data/database/cninfo.db

# 文件存储配置
# TXT文件存储目录
TXT_DIR=txt

# PDF文件存储目录
PDF_DIR=pdf

# 导出文件存储目录
EXPORTS_DIR=exports

# 分析结果存储目录
RESULTS_DIR=results

# Flask应用配置
# 是否启用调试模式
FLASK_DEBUG=True

# Flask应用端口
FLASK_PORT=5000

# Flask应用主机
FLASK_HOST=0.0.0.0

# 日志配置
# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# 爬虫配置
# 请求超时时间（秒）
REQUEST_TIMEOUT=30

# 请求重试次数
REQUEST_RETRIES=3

# 请求间隔时间（秒）
REQUEST_DELAY=1

# AI分析配置
# 上下文长度（字符数）
CONTEXT_LENGTH=300

# 最大上下文数量
MAX_CONTEXTS=10

# AI分析超时时间（秒）
AI_ANALYSIS_TIMEOUT=120
