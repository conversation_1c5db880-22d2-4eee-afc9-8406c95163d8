#!/usr/bin/env python3
"""
测试在线分析流程
"""
import requests
import json
import time
from logger_utils import log_info, log_debug, log_warning, log_error, log_success

def test_analysis_flow():
    """测试完整的在线分析流程"""
    
    base_url = "http://localhost:5000/api"
    
    log_info("开始测试在线分析流程", "Test")
    
    # 1. 测试健康检查
    log_info("步骤1: 健康检查", "Test")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            log_success("健康检查通过", "Test")
        else:
            log_error(f"健康检查失败: {response.status_code}", "Test")
            return
    except Exception as e:
        log_error(f"健康检查异常: {e}", "Test")
        return
    
    # 2. 启动分析任务
    log_info("步骤2: 启动在线分析任务", "Test")
    analysis_params = {
        "stock_codes": "000001\n000002",  # 测试两个股票代码
        "keywords": "创新\n研发",
        "search_keyword": "年度报告",
        "start_date": "2024-01-01",
        "end_date": "2025-12-31",
        "use_online": True,
        "related_parties": ""
    }
    
    try:
        response = requests.post(f"{base_url}/start_analysis", 
                               json=analysis_params, 
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                task_id = data['data']['task_id']
                log_success(f"分析任务启动成功，任务ID: {task_id}", "Test")
                
                # 3. 监控任务进度
                log_info("步骤3: 监控任务进度", "Test")
                monitor_task_progress(base_url, task_id)
                
                # 4. 获取分析结果
                log_info("步骤4: 获取分析结果", "Test")
                get_analysis_results(base_url, task_id)
                
            else:
                log_error(f"启动任务失败: {data.get('message')}", "Test")
        else:
            log_error(f"启动任务HTTP错误: {response.status_code}", "Test")
            log_error(f"响应内容: {response.text}", "Test")
            
    except Exception as e:
        log_error(f"启动任务异常: {e}", "Test")

def monitor_task_progress(base_url, task_id):
    """监控任务进度"""
    max_wait_time = 300  # 最多等待5分钟
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(f"{base_url}/task_status/{task_id}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    task_info = data['data']
                    status = task_info.get('status')
                    progress = task_info.get('progress', 0)
                    message = task_info.get('message', '')
                    current_step = task_info.get('current_step', 0)
                    total_steps = task_info.get('total_steps', 0)
                    
                    log_info(f"任务状态: {status}, 进度: {progress}%, 步骤: {current_step}/{total_steps}, 消息: {message}", "Progress")
                    
                    if status == 'completed':
                        log_success("任务完成", "Progress")
                        break
                    elif status == 'error':
                        log_error(f"任务失败: {message}", "Progress")
                        break
                    elif status == 'stopped':
                        log_warning("任务已停止", "Progress")
                        break
                else:
                    log_error(f"获取任务状态失败: {data.get('message')}", "Progress")
            else:
                log_error(f"获取任务状态HTTP错误: {response.status_code}", "Progress")
                
        except Exception as e:
            log_error(f"获取任务状态异常: {e}", "Progress")
        
        time.sleep(2)  # 每2秒检查一次
    
    if time.time() - start_time >= max_wait_time:
        log_warning("任务监控超时", "Progress")

def get_analysis_results(base_url, task_id):
    """获取分析结果"""
    try:
        response = requests.get(f"{base_url}/analysis_results/{task_id}", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data['data']
                log_success(f"成功获取分析结果，共 {len(results)} 条记录", "Results")
                
                # 显示前几条结果
                for i, result in enumerate(results[:5]):
                    log_info(f"结果{i+1}: {result.get('company_name')} - {result.get('keyword')} - {result.get('count')}次", "Results")
                
                if len(results) > 5:
                    log_info(f"... 还有 {len(results) - 5} 条结果", "Results")
                    
            else:
                log_error(f"获取结果失败: {data.get('message')}", "Results")
        else:
            log_error(f"获取结果HTTP错误: {response.status_code}", "Results")
            log_error(f"响应内容: {response.text[:500]}", "Results")
            
    except Exception as e:
        log_error(f"获取结果异常: {e}", "Results")

if __name__ == "__main__":
    log_info("=" * 60, "Test")
    log_info("在线分析流程测试开始", "Test")
    log_info("=" * 60, "Test")
    
    test_analysis_flow()
    
    log_info("=" * 60, "Test")
    log_info("在线分析流程测试完成", "Test")
    log_info("=" * 60, "Test")
