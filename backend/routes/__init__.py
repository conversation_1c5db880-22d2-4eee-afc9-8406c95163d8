"""
路由模块
"""
from .analysis_routes import analysis_bp
from .data_routes import data_bp
from .ai_routes import ai_bp
from .export_routes import export_bp

def register_routes(app):
    """注册所有路由"""
    app.register_blueprint(analysis_bp, url_prefix='/api')
    app.register_blueprint(data_bp, url_prefix='/api')
    app.register_blueprint(ai_bp, url_prefix='/api')
    app.register_blueprint(export_bp, url_prefix='/api')

__all__ = ['register_routes']
