"""
分析相关路由
"""
from flask import Blueprint, request
from datetime import datetime
from services import SpiderService, AnalysisService
from models import TaskManager
from utils import success_response, error_response, validate_required_fields

analysis_bp = Blueprint('analysis', __name__)

# 全局变量，在app.py中初始化
spider_service = None
analysis_service = None
task_manager = None


def init_analysis_routes(spider_svc, analysis_svc, task_mgr):
    """初始化路由依赖"""
    global spider_service, analysis_service, task_manager
    spider_service = spider_svc
    analysis_service = analysis_svc
    task_manager = task_mgr


def _convert_to_frontend_format(analysis_results, task_id):
    """将嵌套对象格式转换为前端期望的数组格式"""
    from logger_utils import log_info, log_debug, log_warning

    flattened_results = []

    log_debug(f"转换分析结果，原始数据类型: {type(analysis_results)}", "DataConvert")
    log_debug(f"原始数据内容: {analysis_results}", "DataConvert")

    if not analysis_results:
        log_warning("分析结果为空", "DataConvert")
        return flattened_results

    # 获取公司信息映射
    try:
        companies = analysis_service.db.get_companies()
        company_map = {c.get('stock_code'): c.get('company_name', c.get('stock_code')) for c in companies}
        log_debug(f"获取到 {len(companies)} 个公司信息", "DataConvert")
    except Exception as e:
        log_warning(f"获取公司信息失败: {e}", "DataConvert")
        company_map = {}

    # 转换嵌套对象为数组
    for stock_code, files in analysis_results.items():
        log_debug(f"处理股票代码: {stock_code}, 文件数据类型: {type(files)}", "DataConvert")

        if not isinstance(files, dict):
            log_warning(f"股票 {stock_code} 的文件数据不是字典格式: {files}", "DataConvert")
            continue

        company_name = company_map.get(stock_code, stock_code)

        for file_name, keywords in files.items():
            log_debug(f"处理文件: {file_name}, 关键词数据类型: {type(keywords)}", "DataConvert")

            if not isinstance(keywords, dict):
                log_warning(f"文件 {file_name} 的关键词数据不是字典格式: {keywords}", "DataConvert")
                continue

            for keyword, count in keywords.items():
                result_item = {
                    'stock_code': stock_code,
                    'company_name': company_name,
                    'file_name': file_name,
                    'keyword': keyword,
                    'count': int(count) if isinstance(count, (int, str)) else 0,
                    'analysis_id': task_id
                }
                flattened_results.append(result_item)
                log_debug(f"添加结果项: {result_item}", "DataConvert")

    log_info(f"数据转换完成，共生成 {len(flattened_results)} 条结果", "DataConvert")
    return flattened_results


@analysis_bp.route('/start_analysis', methods=['POST'])
def start_analysis():
    """开始分析任务"""
    print(f"🔍 收到开始分析请求")
    print(f"   请求方法: {request.method}")
    print(f"   请求头: {dict(request.headers)}")

    try:
        data = request.json
        print(f"   请求数据: {data}")
        
        # 验证必需字段
        required_fields = ['stock_codes', 'keywords']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return error_response(validation_error)
        
        # 解析参数
        stock_codes = [code.strip() for code in data.get('stock_codes', '').split('\n') if code.strip()]
        keywords = [kw.strip() for kw in data.get('keywords', '').split('\n') if kw.strip()]
        search_keyword = data.get('search_keyword', '年度报告')
        start_date = data.get('start_date', '2024-01-01')
        end_date = data.get('end_date', '2025-12-31')
        use_online = data.get('use_online', True)
        related_parties = [rp.strip() for rp in data.get('related_parties', '').split('\n') if rp.strip()]

        print(f"📊 分析参数:")
        print(f"   股票代码: {stock_codes}")
        print(f"   关键词: {keywords}")
        print(f"   搜索关键词: {search_keyword}")
        print(f"   时间范围: {start_date} ~ {end_date}")
        print(f"   使用在线模式: {use_online}")
        print(f"   关联方: {related_parties}")
        
        if not stock_codes:
            return error_response('请输入股票代码')
        
        if not keywords:
            return error_response('请输入关键词')
        
        # 创建任务
        task_name = f"关键词分析_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        task_id = task_manager.create_task(
            task_name=task_name,
            keywords=keywords,
            stock_codes=stock_codes
        )
        
        # 定义进度回调
        def progress_callback(current, total, message):
            task_manager.update_progress(task_id, current, total, message)
        
        # 定义任务函数
        def run_analysis_task(received_task_id):
            from logger_utils import log_info, log_debug, log_error

            try:
                # 使用传入的task_id（应该与创建的task_id相同）
                actual_task_id = received_task_id or task_id
                log_info(f"开始执行分析任务，任务ID: {actual_task_id}", "TaskExecution")
                log_debug(f"分析参数: 股票代码={stock_codes}, 关键词={keywords}", "TaskExecution")

                results = spider_service.crawl_and_analyze(
                    stock_codes=stock_codes,
                    keywords=keywords,
                    search_keyword=search_keyword,
                    start_date=start_date,
                    end_date=end_date,
                    use_online=use_online,
                    task_id=actual_task_id,
                    related_parties=related_parties,
                    innovation_keywords=keywords,
                    progress_callback=progress_callback
                )

                log_info(f"分析任务完成，结果类型: {type(results)}", "TaskExecution")
                log_debug(f"分析结果: {results}", "TaskExecution")

                return results
            except Exception as e:
                log_error(f"分析任务执行失败: {e}", "TaskExecution")
                raise e
        
        # 启动异步任务
        task_manager.run_task_async(task_id, run_analysis_task)
        
        return success_response({
            'task_id': task_id,
            'message': '任务已启动'
        })
        
    except Exception as e:
        return error_response(f'启动任务失败: {str(e)}')


@analysis_bp.route('/keyword_analysis', methods=['POST'])
def keyword_analysis_only():
    """仅进行关键词分析（使用本地数据）"""
    print(f"🔍 收到关键词分析请求")
    print(f"   请求方法: {request.method}")
    print(f"   请求头: {dict(request.headers)}")

    try:
        data = request.json
        print(f"   请求数据: {data}")
        
        # 验证必需字段
        required_fields = ['stock_codes', 'keywords']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return error_response(validation_error)
        
        # 解析参数
        stock_codes = [code.strip() for code in data.get('stock_codes', '').split('\n') if code.strip()]
        keywords = [kw.strip() for kw in data.get('keywords', '').split('\n') if kw.strip()]
        related_parties = [rp.strip() for rp in data.get('related_parties', '').split('\n') if rp.strip()]
        
        if not stock_codes:
            return error_response('请输入股票代码')
        
        if not keywords:
            return error_response('请输入关键词')
        
        # 执行分析
        result = analysis_service.keyword_analysis_only(stock_codes, keywords, related_parties)

        print(f"✅ 关键词分析完成，结果数量: {len(result.get('data', []))}")

        return success_response(result, message='关键词分析完成')
        
    except Exception as e:
        return error_response(f'分析失败: {str(e)}')


@analysis_bp.route('/task_status/<task_id>')
def get_task_status(task_id):
    """获取任务状态"""
    try:
        task_info = task_manager.get_task(task_id)
        if task_info:
            return success_response(task_info)
        else:
            return error_response('任务不存在')
    except Exception as e:
        return error_response(f'获取任务状态失败: {str(e)}')


@analysis_bp.route('/stop_task/<task_id>', methods=['POST'])
def stop_task(task_id):
    """停止任务"""
    try:
        spider_service.stop_crawling()
        task_manager.stop_task(task_id)
        
        return success_response({'message': '任务已停止'})
    except Exception as e:
        return error_response(f'停止任务失败: {str(e)}')


@analysis_bp.route('/analysis_results/<task_id>')
def get_analysis_results(task_id):
    """获取分析结果"""
    from logger_utils import log_info, log_debug, log_warning, log_error

    try:
        log_info(f"获取分析结果，任务ID: {task_id}", "AnalysisResults")

        # 先从任务管理器获取
        task_info = task_manager.get_task(task_id)
        log_debug(f"任务信息: {task_info}", "AnalysisResults")

        if task_info and task_info.get('results'):
            log_info("从任务管理器获取结果", "AnalysisResults")
            results = task_info['results']
            log_debug(f"任务结果类型: {type(results)}", "AnalysisResults")
            log_debug(f"任务结果键: {list(results.keys()) if isinstance(results, dict) else 'Not a dict'}", "AnalysisResults")

            # 提取analysis_results部分
            analysis_results = results.get('analysis_results', results)
            log_debug(f"分析结果类型: {type(analysis_results)}", "AnalysisResults")
            log_debug(f"分析结果内容: {analysis_results}", "AnalysisResults")

            # 转换为前端期望的数组格式
            flattened_results = _convert_to_frontend_format(analysis_results, task_id)
            log_info(f"返回 {len(flattened_results)} 条结果", "AnalysisResults")
            return success_response(flattened_results)
        else:
            log_info("从数据库获取结果", "AnalysisResults")
            # 从数据库获取结果
            analysis_results = analysis_service.db.get_keyword_analysis(task_id)
            log_debug(f"数据库结果: {analysis_results}", "AnalysisResults")

            # 转换为前端期望的数组格式
            flattened_results = _convert_to_frontend_format(analysis_results, task_id)
            log_info(f"返回 {len(flattened_results)} 条结果", "AnalysisResults")
            return success_response(flattened_results)
    except Exception as e:
        log_error(f'获取结果失败: {str(e)}', "AnalysisResults")
        import traceback
        log_error(f'错误详情: {traceback.format_exc()}', "AnalysisResults")
        return error_response(f'获取结果失败: {str(e)}')


@analysis_bp.route('/keyword_context/<analysis_id>/<keyword>', methods=['GET'])
def get_keyword_context(analysis_id, keyword):
    """获取关键词上下文片段"""
    try:
        # 获取参数
        context_length = request.args.get('context_length', '100')
        try:
            context_length = int(context_length)
        except:
            context_length = 100
        
        stock_code_filter = request.args.get('stock_code')
        file_name_filter = request.args.get('file_name')
        
        # 获取上下文
        result = analysis_service.get_keyword_context(
            analysis_id, keyword, context_length,
            stock_code_filter, file_name_filter
        )

        print(f"✅ 上下文获取完成，返回 {len(result.get('contexts', []))} 个上下文")

        return success_response(result, message='上下文获取成功')
        
    except Exception as e:
        return error_response(f'获取上下文失败: {str(e)}')
