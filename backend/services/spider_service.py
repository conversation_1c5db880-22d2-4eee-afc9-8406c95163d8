"""
爬虫服务模块
"""
import requests
import random
import time
import os
import re
import pdfplumber
from typing import List, Dict, Optional, Tuple, Callable
from models.database import DatabaseManager
from logger_utils import log_info, log_debug, log_warning, log_error, log_success


class SpiderService:
    """爬虫服务类"""
    
    def __init__(self, db_manager: DatabaseManager):
        """初始化爬虫服务"""
        self.db = db_manager
        self.is_running = False
        
        # 用户代理列表
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ]
        
        # 请求头
        self.headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Accept-Encoding': 'gzip, deflate',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Host': 'www.cninfo.com.cn',
            'Origin': 'http://www.cninfo.com.cn',
            'Referer': 'http://www.cninfo.com.cn/new/commonUrl?url=disclosure/list/notice',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        # API URLs
        self.orgid_url = 'http://www.cninfo.com.cn/new/data/szse_stock.json'
        self.query_url = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
        self.download_base_url = 'http://static.cninfo.com.cn/'
    
    def get_orgid_by_code(self, stock_code: str) -> Optional[Dict]:
        """根据股票代码获取orgId"""
        try:
            log_debug(f"获取股票代码 {stock_code} 的orgId", "SpiderService")
            response = requests.get(self.orgid_url, headers=self.headers, timeout=10)
            log_debug(f"orgId API响应状态: {response.status_code}", "SpiderService")

            if response.status_code == 200:
                data = response.json()
                stock_lists = data.get('stockList', [])
                log_debug(f"获取到 {len(stock_lists)} 个股票信息", "SpiderService")

                for stock_info in stock_lists:
                    if stock_info.get('code') == stock_code:
                        result = {
                            'code': stock_info['code'],
                            'orgId': stock_info['orgId'],
                            'zwjc': stock_info.get('zwjc', ''),
                        }
                        log_debug(f"找到股票 {stock_code} 的信息: {result}", "SpiderService")
                        return result

                log_warning(f"在 {len(stock_lists)} 个股票中未找到代码 {stock_code}", "SpiderService")
            else:
                log_error(f"获取股票列表失败: HTTP {response.status_code}", "SpiderService")
            return None
        except Exception as e:
            log_error(f"获取orgId失败: {e}", "SpiderService")
            return None
    
    def search_announcements(self, stock_code: str, org_id: str, 
                           search_keyword: str = "年度报告", 
                           start_date: str = "2024-01-01", 
                           end_date: str = "2025-12-31") -> List[Dict]:
        """搜索公告"""
        try:
            self.headers['User-Agent'] = random.choice(self.user_agents)
            
            # 深市查询
            szse_query = {
                'pageNum': 1,
                'pageSize': 30,
                'tabName': 'fulltext',
                'column': 'szse',
                'stock': f'{stock_code},{org_id}',
                'searchkey': '',
                'secid': '',
                'plate': 'sz',
                'category': 'category_ndbg_szsh',
                'trade': '',
                'seDate': f'{start_date}~{end_date}',
                'sortName': '',
                'sortType': '',
                'isHLtitle': 'true'
            }
            
            # 沪市查询
            sse_query = szse_query.copy()
            sse_query.update({
                'column': 'sse',
                'plate': 'sh'
            })
            
            announcements = []
            
            # 查询深市
            try:
                log_debug("查询深市数据...", "SpiderService")
                response = requests.post(self.query_url, data=szse_query, headers=self.headers, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('announcements'):
                        announcements.extend(data['announcements'])
                        log_info(f"深市找到 {len(data['announcements'])} 条公告", "SpiderService")
                    else:
                        log_warning("深市未找到公告", "SpiderService")
                time.sleep(1)
            except Exception as e:
                log_error(f"深市查询失败: {e}", "SpiderService")
            
            # 查询沪市
            try:
                log_debug("查询沪市数据...", "SpiderService")
                response = requests.post(self.query_url, data=sse_query, headers=self.headers, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('announcements'):
                        announcements.extend(data['announcements'])
                        log_info(f"沪市找到 {len(data['announcements'])} 条公告", "SpiderService")
                    else:
                        log_warning("沪市未找到公告", "SpiderService")
                time.sleep(1)
            except Exception as e:
                log_error(f"沪市查询失败: {e}", "SpiderService")

            return announcements

        except Exception as e:
            log_error(f"搜索公告失败: {e}", "SpiderService")
            return []
    
    def download_pdf(self, announcement: Dict, pdf_dir: str = "pdf") -> Optional[str]:
        """下载PDF文件"""
        try:
            if not os.path.exists(pdf_dir):
                os.makedirs(pdf_dir)
            
            adj_url = announcement.get('adjunctUrl', '')
            if not adj_url:
                return None
            
            download_url = self.download_base_url + adj_url
            filename = f"{announcement.get('secCode', 'unknown')}_{announcement.get('secName', 'unknown')}_{announcement.get('announcementTitle', 'unknown')}.pdf"
            
            # 清理文件名中的非法字符
            filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
            filepath = os.path.join(pdf_dir, filename)
            
            # 检查文件是否已存在
            if os.path.exists(filepath):
                log_info(f"PDF文件已存在: {filename}", "SpiderService")
                return filepath

            log_info(f"下载PDF: {filename}", "SpiderService")
            response = requests.get(download_url, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                log_success(f"PDF下载成功: {filename}", "SpiderService")
                return filepath
            else:
                log_error(f"PDF下载失败: HTTP {response.status_code}", "SpiderService")
                return None

        except Exception as e:
            log_error(f"PDF下载异常: {e}", "SpiderService")
            return None
    
    def pdf_to_text(self, pdf_path: str, txt_dir: str = "txt") -> Optional[str]:
        """PDF转文本"""
        try:
            if not os.path.exists(txt_dir):
                os.makedirs(txt_dir)
            
            # 生成TXT文件路径
            pdf_filename = os.path.basename(pdf_path)
            txt_filename = pdf_filename.replace('.pdf', '.txt')
            txt_path = os.path.join(txt_dir, txt_filename)
            
            # 检查TXT文件是否已存在
            if os.path.exists(txt_path):
                log_info(f"TXT文件已存在: {txt_filename}", "SpiderService")
                return txt_path

            log_info(f"转换PDF到TXT: {txt_filename}", "SpiderService")

            text_content = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text_content += page_text + "\n"

            if text_content.strip():
                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
                log_success(f"TXT转换成功: {txt_filename}", "SpiderService")
                return txt_path
            else:
                log_warning("PDF内容为空", "SpiderService")
                return None

        except Exception as e:
            log_error(f"PDF转换失败: {e}", "SpiderService")
            return None
    
    def stop_crawling(self):
        """停止爬虫"""
        self.is_running = False
    
    def crawl_and_analyze(self, stock_codes: List[str], keywords: List[str],
                         search_keyword: str = "年度报告",
                         start_date: str = "2024-01-01",
                         end_date: str = "2025-12-31",
                         use_online: bool = True,
                         task_id: str = None,
                         related_parties: List[str] = None,
                         innovation_keywords: List[str] = None,
                         progress_callback: Callable = None) -> Dict:
        """爬取并分析年报"""
        
        self.is_running = True
        results = {
            'analysis_results': {},
            'related_party_analysis': {},
            'download_results': {},
            'summary': {
                'total_companies': len(stock_codes),
                'successful_downloads': 0,
                'failed_downloads': 0,
                'total_keywords_found': 0
            }
        }
        
        try:
            for i, stock_code in enumerate(stock_codes):
                if not self.is_running:
                    break
                
                if progress_callback:
                    progress_callback(i, len(stock_codes), f"处理股票代码: {stock_code}")
                
                log_info(f"[{i+1}/{len(stock_codes)}] 处理股票代码: {stock_code}", "SpiderService")

                # 获取orgId
                org_info = self.get_orgid_by_code(stock_code)
                if not org_info:
                    log_warning(f"未找到股票代码 {stock_code} 的orgId", "SpiderService")
                    results['summary']['failed_downloads'] += 1
                    continue

                company_name = org_info.get('zwjc', stock_code)
                org_id = org_info['orgId']
                log_info(f"找到公司信息: {company_name} (orgId: {org_id})", "SpiderService")

                # 添加公司信息到数据库
                self.db.add_company(stock_code, company_name, org_id)
                
                if use_online:
                    log_info(f"使用在线模式分析股票 {stock_code}", "SpiderService")
                    # 首先检查本地数据库是否已有该公司的年报
                    local_reports = self.db.get_reports_by_stock_codes([stock_code])
                    log_info(f"本地数据库中找到 {len(local_reports)} 个年报", "SpiderService")

                    if local_reports:
                        # 过滤本地年报，只使用时间范围内的年报
                        filtered_local_reports = self._filter_reports_by_date_range(
                            local_reports, start_date, end_date
                        )
                        log_info(f"时间范围过滤后剩余 {len(filtered_local_reports)} 个年报", "SpiderService")

                        if filtered_local_reports:
                            log_info(f"本地数据库中找到 {len(filtered_local_reports)} 个符合时间范围的年报，使用本地数据", "SpiderService")

                            # 更新进度：开始处理本地数据
                            if progress_callback:
                                progress_callback(i, len(stock_codes), f"使用本地数据分析: {stock_code}")

                            # 使用本地数据进行分析
                            for report in filtered_local_reports:
                                if not self.is_running:
                                    break

                                txt_content = report.get('txt_content', '')
                                if not txt_content:
                                    continue

                                file_name = report.get('file_name', '')

                                # 关键词分析
                                keyword_stats = self.analyze_keywords(txt_content, keywords)

                                # 保存分析结果到内存
                                if stock_code not in results['analysis_results']:
                                    results['analysis_results'][stock_code] = {}
                                results['analysis_results'][stock_code][file_name] = keyword_stats

                                # 保存分析结果到数据库（用于上下文查看）
                                if task_id:
                                    self.db.save_keyword_analysis(
                                        analysis_id=task_id,
                                        stock_code=stock_code,
                                        report_id=report['id'],
                                        keyword_stats=keyword_stats
                                    )

                                # 关联方分析
                                if related_parties and innovation_keywords:
                                    related_analysis = self.analyze_related_parties(
                                        txt_content, related_parties, innovation_keywords
                                    )
                                    if related_analysis:
                                        if stock_code not in results['related_party_analysis']:
                                            results['related_party_analysis'][stock_code] = {}
                                        results['related_party_analysis'][stock_code][file_name] = related_analysis

                                results['summary']['successful_downloads'] += 1

                        # 跳过在线下载，继续处理下一个股票代码
                        continue

                    else:
                        log_info(f"本地数据库中找到 {len(local_reports)} 个年报，但都不在指定时间范围内({start_date}~{end_date})，进行在线搜索", "SpiderService")

                    # 本地没有符合时间范围的数据，进行在线搜索和下载
                    if not local_reports:
                        log_info("本地数据库中未找到年报，开始在线搜索...", "SpiderService")
                    # 如果有local_reports但没有filtered_local_reports，上面已经记录了日志

                    # 更新进度：开始在线搜索
                    if progress_callback:
                        progress_callback(i, len(stock_codes), f"在线搜索年报: {stock_code}")

                    announcements = self.search_announcements(
                        stock_code, org_id, search_keyword, start_date, end_date
                    )

                    if not announcements:
                        log_warning("未找到相关公告", "SpiderService")
                        continue
                    
                    # 过滤并处理公告
                    filtered_announcements = []
                    for announcement in announcements:
                        title = announcement.get('announcementTitle', '')
                        # 更严格的过滤条件：
                        # 1. 不包含"摘要"
                        # 2. 包含"年度报告"或"年报"
                        # 3. 不包含"更正"、"补充"、"修订"等
                        exclude_keywords = ['摘要', '更正', '补充', '修订', '取消', '致歉']
                        include_keywords = ['年度报告', '年报']

                        # 检查是否包含排除关键词
                        has_exclude = any(keyword in title for keyword in exclude_keywords)
                        # 检查是否包含必需关键词
                        has_include = any(keyword in title for keyword in include_keywords)

                        if not has_exclude and has_include:
                            filtered_announcements.append(announcement)
                            log_info(f"找到完整年报: {title}", "SpiderService")
                        else:
                            log_debug(f"跳过非完整年报: {title}", "SpiderService")

                    if not filtered_announcements:
                        log_warning("过滤后没有找到完整年报", "SpiderService")
                        continue

                    # 按发布时间排序，优先处理最新的
                    filtered_announcements.sort(key=lambda x: x.get('announcementTime', ''), reverse=True)

                    # 处理过滤后的公告（优先处理最新的）
                    for j, announcement in enumerate(filtered_announcements):
                        if not self.is_running:
                            break

                        # 更新进度：下载和处理年报
                        if progress_callback:
                            progress_callback(i, len(stock_codes), f"下载年报 {j+1}/{len(filtered_announcements)}: {stock_code}")

                        # 下载PDF
                        pdf_path = self.download_pdf(announcement)
                        if not pdf_path:
                            continue

                        # 转换为TXT
                        txt_path = self.pdf_to_text(pdf_path)
                        if not txt_path:
                            continue
                        
                        # 读取TXT内容
                        try:
                            with open(txt_path, 'r', encoding='utf-8') as f:
                                txt_content = f.read()
                        except Exception as e:
                            log_error(f"读取TXT文件失败: {e}", "SpiderService")
                            continue
                        
                        # 保存到数据库
                        report_title = announcement.get('announcementTitle', '')
                        file_name = os.path.basename(txt_path)
                        
                        self.db.add_report(
                            stock_code=stock_code,
                            company_name=company_name,
                            report_title=report_title,
                            file_name=file_name,
                            file_path=txt_path,
                            txt_content=txt_content
                        )
                        
                        # 关键词分析
                        keyword_stats = self.analyze_keywords(txt_content, keywords)

                        # 保存分析结果到内存
                        if stock_code not in results['analysis_results']:
                            results['analysis_results'][stock_code] = {}
                        results['analysis_results'][stock_code][file_name] = keyword_stats

                        # 保存分析结果到数据库（用于上下文查看）
                        if task_id:
                            # 获取报告ID
                            report_id = self.db.get_report_id_by_file_path(txt_path)
                            if report_id:
                                # 保存关键词分析结果（传递整个字典）
                                self.db.save_keyword_analysis(
                                    analysis_id=task_id,
                                    stock_code=stock_code,
                                    report_id=report_id,
                                    keyword_stats=keyword_stats
                                )
                        
                        # 关联方分析
                        if related_parties and innovation_keywords:
                            related_analysis = self.analyze_related_parties(
                                txt_content, related_parties, innovation_keywords
                            )
                            if related_analysis:
                                if stock_code not in results['related_party_analysis']:
                                    results['related_party_analysis'][stock_code] = {}
                                results['related_party_analysis'][stock_code][file_name] = related_analysis
                        
                        results['summary']['successful_downloads'] += 1
                        break  # 只处理第一个找到的年报
                
                else:
                    # 使用本地数据
                    log_info(f"使用本地模式分析股票 {stock_code}", "SpiderService")
                    reports = self.db.get_reports_by_stock_codes([stock_code])
                    log_info(f"本地数据库中找到 {len(reports)} 个年报", "SpiderService")

                    for report in reports:
                        if not self.is_running:
                            break
                        
                        txt_content = report.get('txt_content', '')
                        if not txt_content:
                            continue
                        
                        file_name = report.get('file_name', '')
                        
                        # 关键词分析
                        keyword_stats = self.analyze_keywords(txt_content, keywords)
                        
                        # 保存分析结果
                        if stock_code not in results['analysis_results']:
                            results['analysis_results'][stock_code] = {}
                        results['analysis_results'][stock_code][file_name] = keyword_stats
                        
                        # 关联方分析
                        if related_parties and innovation_keywords:
                            related_analysis = self.analyze_related_parties(
                                txt_content, related_parties, innovation_keywords
                            )
                            if related_analysis:
                                if stock_code not in results['related_party_analysis']:
                                    results['related_party_analysis'][stock_code] = {}
                                results['related_party_analysis'][stock_code][file_name] = related_analysis

            # 计算总关键词出现次数
            for stock_results in results['analysis_results'].values():
                for file_results in stock_results.values():
                    for count in file_results.values():
                        results['summary']['total_keywords_found'] += count

            if progress_callback:
                progress_callback(len(stock_codes), len(stock_codes), "分析完成")

            return results
            
        except Exception as e:
            log_error(f"爬取分析过程出错: {e}", "SpiderService")
            raise e
    
    def analyze_keywords(self, text: str, keywords: List[str]) -> Dict[str, int]:
        """分析关键词出现次数"""
        # 清理文本，只保留中文字符
        clean_text = re.sub(r'[^\u4e00-\u9fa5]', '', text)
        
        keyword_stats = {}
        for keyword in keywords:
            count = clean_text.count(keyword)
            keyword_stats[keyword] = count
        
        return keyword_stats
    
    def analyze_related_parties(self, text: str, related_parties: List[str], 
                               innovation_keywords: List[str]) -> Dict:
        """分析关联方协同创新"""
        results = {}
        
        for party in related_parties:
            if party in text:
                # 找到关联方提及的上下文
                contexts = self.find_contexts_with_keywords(text, party, innovation_keywords)
                if contexts:
                    results[party] = {
                        'found': True,
                        'contexts': contexts,
                        'innovation_keywords_found': list(set(
                            kw for context in contexts for kw in context['keywords_found']
                        ))
                    }
                else:
                    results[party] = {
                        'found': True,
                        'contexts': [],
                        'innovation_keywords_found': []
                    }
            else:
                results[party] = {
                    'found': False,
                    'contexts': [],
                    'innovation_keywords_found': []
                }
        
        return results
    
    def find_contexts_with_keywords(self, text: str, party: str, 
                                   keywords: List[str], context_length: int = 200) -> List[Dict]:
        """查找包含关键词的上下文"""
        contexts = []
        
        # 找到所有关联方出现的位置
        party_positions = []
        start = 0
        while True:
            pos = text.find(party, start)
            if pos == -1:
                break
            party_positions.append(pos)
            start = pos + 1
        
        # 对每个位置检查周围是否有创新关键词
        for pos in party_positions:
            start_pos = max(0, pos - context_length)
            end_pos = min(len(text), pos + len(party) + context_length)
            context_text = text[start_pos:end_pos]
            
            # 检查上下文中的关键词
            keywords_found = []
            for keyword in keywords:
                if keyword in context_text:
                    keywords_found.append(keyword)
            
            if keywords_found:
                contexts.append({
                    'text': context_text,
                    'keywords_found': keywords_found,
                    'position': pos
                })
        
        return contexts

    def _filter_reports_by_date_range(self, reports: List[Dict], start_date: str, end_date: str) -> List[Dict]:
        """根据时间范围过滤年报"""
        from datetime import datetime
        import re

        filtered_reports = []

        try:
            # 解析时间范围
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

            for report in reports:
                report_title = report.get('report_title', '')
                file_name = report.get('file_name', '')

                # 从标题或文件名中提取年份
                report_year = self._extract_year_from_report(report_title, file_name)

                if report_year:
                    try:
                        # 假设年报发布时间为该年的12月31日
                        report_dt = datetime(report_year, 12, 31)

                        # 检查是否在时间范围内
                        if start_dt <= report_dt <= end_dt:
                            filtered_reports.append(report)
                            log_debug(f"年报 {report_title} ({report_year}年) 在时间范围内", "SpiderService")
                        else:
                            log_debug(f"年报 {report_title} ({report_year}年) 不在时间范围内", "SpiderService")
                    except ValueError:
                        log_warning(f"无法解析年报时间: {report_title}", "SpiderService")
                        continue
                else:
                    log_warning(f"无法从年报标题中提取年份: {report_title}", "SpiderService")
                    continue

        except Exception as e:
            log_error(f"过滤年报时间范围失败: {e}", "SpiderService")
            return reports  # 如果过滤失败，返回原始列表

        return filtered_reports

    def _extract_year_from_report(self, report_title: str, file_name: str) -> Optional[int]:
        """从年报标题或文件名中提取年份"""
        import re

        # 尝试从标题和文件名中提取年份
        text_sources = [report_title, file_name]

        for text in text_sources:
            if not text:
                continue

            # 查找4位数年份（2020-2030范围内）
            year_matches = re.findall(r'(20[2-3][0-9])', text)
            if year_matches:
                # 返回最大的年份（通常是最相关的）
                return max(int(year) for year in year_matches)

            # 查找"XXXX年"格式
            year_matches = re.findall(r'(20[2-3][0-9])年', text)
            if year_matches:
                return max(int(year) for year in year_matches)

        return None
