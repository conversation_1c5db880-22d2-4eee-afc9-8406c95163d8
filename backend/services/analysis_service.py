"""
分析服务模块
"""
import re
import time
from typing import List, Dict, Optional, Any
from models.database import DatabaseManager


class AnalysisService:
    """分析服务类"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def keyword_analysis_only(self, stock_codes: List[str], keywords: List[str], 
                             related_parties: List[str] = None) -> Dict[str, Any]:
        """仅进行关键词分析（使用本地数据）"""
        print("🔍 开始关键词分析...")
        
        # 获取本地年报数据
        print("📊 查询本地年报数据...")
        reports = self.db.get_reports_by_stock_codes(stock_codes)
        print(f"📄 找到 {len(reports)} 个年报")
        
        if not reports:
            raise ValueError("没有找到相关的年报数据，请先导入txt文件或在线下载")
        
        # 生成分析ID
        analysis_id = f"keyword_analysis_{int(time.time())}"
        
        # 创建与"开始分析"一致的结果格式
        analysis_results = {}
        related_party_analysis = {}
        
        print("🔍 开始分析关键词...")
        # 分析每个年报
        for i, report in enumerate(reports, 1):
            print(f"[{i}/{len(reports)}] 分析: {report.get('file_name', 'Unknown')}")
            if report.get('txt_content'):
                keyword_stats = self.analyze_keywords(report['txt_content'], keywords)
                print(f"  📊 关键词统计: {keyword_stats}")
                
                # 保存分析结果到数据库
                self.db.save_keyword_analysis(
                    analysis_id, report['stock_code'], report['id'], keyword_stats
                )
                
                # 构建与"开始分析"一致的数据结构
                stock_code = report['stock_code']
                file_name = report.get('file_name', '')
                
                if stock_code not in analysis_results:
                    analysis_results[stock_code] = {}
                
                analysis_results[stock_code][file_name] = keyword_stats
                
                # 关联方分析
                if related_parties and keywords:
                    related_analysis = self.analyze_related_parties(
                        report['txt_content'], related_parties, keywords
                    )
                    if related_analysis:
                        if stock_code not in related_party_analysis:
                            related_party_analysis[stock_code] = {}
                        related_party_analysis[stock_code][file_name] = related_analysis
        
        print("✅ 关键词分析完成")
        
        return {
            'success': True,
            'analysis_id': analysis_id,
            'data': analysis_results,
            'related_party_analysis': related_party_analysis,
            'message': f'分析完成，共分析 {len(reports)} 个年报文件'
        }
    
    def analyze_keywords(self, text: str, keywords: List[str]) -> Dict[str, int]:
        """分析关键词出现次数"""
        # 不要过度清理文本，保留中英文、数字和基本标点
        # 只移除一些特殊字符和多余空白
        clean_text = re.sub(r'[\s\u3000]+', '', text)  # 移除空白字符

        keyword_stats = {}
        for keyword in keywords:
            # 直接在原文中搜索关键词
            count_original = text.count(keyword)
            # 在清理后的文本中搜索
            count_clean = clean_text.count(keyword)
            # 使用较大的计数
            count = max(count_original, count_clean)
            keyword_stats[keyword] = count

        return keyword_stats
    
    def analyze_related_parties(self, text: str, related_parties: List[str], 
                               innovation_keywords: List[str]) -> Dict:
        """分析关联方协同创新"""
        results = {}
        
        for party in related_parties:
            if party in text:
                # 找到关联方提及的上下文
                contexts = self.find_contexts_with_keywords(text, party, innovation_keywords)
                if contexts:
                    results[party] = {
                        'found': True,
                        'contexts': contexts,
                        'innovation_keywords_found': list(set(
                            kw for context in contexts for kw in context['keywords_found']
                        ))
                    }
                else:
                    results[party] = {
                        'found': True,
                        'contexts': [],
                        'innovation_keywords_found': []
                    }
            else:
                results[party] = {
                    'found': False,
                    'contexts': [],
                    'innovation_keywords_found': []
                }
        
        return results
    
    def find_contexts_with_keywords(self, text: str, party: str, 
                                   keywords: List[str], context_length: int = 200) -> List[Dict]:
        """查找包含关键词的上下文"""
        contexts = []
        
        # 找到所有关联方出现的位置
        party_positions = []
        start = 0
        while True:
            pos = text.find(party, start)
            if pos == -1:
                break
            party_positions.append(pos)
            start = pos + 1
        
        # 对每个位置检查周围是否有创新关键词
        for pos in party_positions:
            start_pos = max(0, pos - context_length)
            end_pos = min(len(text), pos + len(party) + context_length)
            context_text = text[start_pos:end_pos]
            
            # 检查上下文中的关键词
            keywords_found = []
            for keyword in keywords:
                if keyword in context_text:
                    keywords_found.append(keyword)
            
            if keywords_found:
                contexts.append({
                    'text': context_text,
                    'keywords_found': keywords_found,
                    'position': pos
                })
        
        return contexts
    
    def get_keyword_context(self, analysis_id: str, keyword: str, 
                           context_length: int = 100,
                           stock_code_filter: str = None,
                           file_name_filter: str = None) -> Dict[str, Any]:
        """获取关键词上下文片段"""
        print(f"🔍 获取上下文: analysis_id={analysis_id}, keyword={keyword}")
        print(f"📏 上下文长度设置: {context_length} 字符")
        
        if stock_code_filter:
            print(f"🎯 只搜索股票代码: {stock_code_filter}")
        if file_name_filter:
            print(f"📄 只搜索文件: {file_name_filter}")
        
        # 从数据库获取分析结果
        analysis_results = self.db.get_keyword_analysis(analysis_id)
        print(f"📊 找到 {len(analysis_results)} 条分析结果")
        
        contexts = []
        for result in analysis_results:
            # 应用过滤条件
            if result['keyword'] == keyword and result['count'] > 0:
                # 检查股票代码过滤
                if stock_code_filter and result.get('stock_code') != stock_code_filter:
                    continue
                
                # 检查文件名过滤
                if file_name_filter and result.get('file_name') != file_name_filter:
                    continue
                
                print(f"  🎯 匹配关键词: {result['stock_code']} - {result.get('file_name', 'Unknown')} - {keyword} ({result['count']}次)")
                
                # 直接通过report_id获取年报内容
                report_id = result.get('report_id')
                if report_id:
                    print(f"  📄 查找年报ID: {report_id}")
                    report = self.db.get_report_by_id(report_id)
                    
                    if report and report.get('txt_content'):
                        print(f"    ✅ 找到年报，文本长度: {len(report['txt_content'])}")
                        
                        # 提取关键词上下文
                        context_snippets = self.extract_keyword_context(
                            report['txt_content'], keyword, context_length
                        )
                        if context_snippets:
                            contexts.append({
                                'stock_code': result['stock_code'],
                                'company_name': result.get('company_name', ''),
                                'file_name': result.get('file_name', ''),
                                'snippets': context_snippets,
                                'total_count': len(context_snippets),
                                'keyword_count': result['count']
                            })
                            print(f"    📝 添加了 {len(context_snippets)} 个上下文片段")
                        else:
                            print(f"    ⚠️ 关键词'{keyword}'上下文提取失败")
        
        # 计算总片段数
        total_snippets = sum(len(ctx.get('snippets', [])) for ctx in contexts)
        
        if stock_code_filter or file_name_filter:
            filter_info = []
            if stock_code_filter:
                filter_info.append(f"股票:{stock_code_filter}")
            if file_name_filter:
                filter_info.append(f"文件:{file_name_filter}")
            print(f"🎉 在过滤条件({', '.join(filter_info)})下找到 {len(contexts)} 个文件，共 {total_snippets} 个上下文片段")
        else:
            print(f"🎉 总共找到 {len(contexts)} 个文件，共 {total_snippets} 个上下文片段")
        
        return {
            'success': True,
            'keyword': keyword,
            'contexts': contexts
        }
    
    def extract_keyword_context(self, text: str, keyword: str, context_length: int = 200) -> List[str]:
        """提取关键词上下文片段"""
        print(f"    🔧 extract_keyword_context调用: keyword='{keyword}', context_length={context_length}")
        
        # 使用与关键词统计相同的文本清理方式
        clean_text_for_search = re.sub(r'[^\u4e00-\u9fa5]', '', text)
        
        # 找到所有关键词位置（在清理后的文本中）
        keyword_positions = []
        start = 0
        while True:
            pos = clean_text_for_search.find(keyword, start)
            if pos == -1:
                break
            keyword_positions.append(pos)
            start = pos + 1
        
        print(f"    🔍 在清理后的文本中找到 {len(keyword_positions)} 个'{keyword}'位置")
        
        if not keyword_positions:
            return []
        
        # 为了显示上下文，我们需要在原始文本中找到对应位置
        original_text = text
        clean_to_original_map = []
        
        for i, char in enumerate(original_text):
            if re.match(r'[\u4e00-\u9fa5]', char):  # 如果是中文字符
                clean_to_original_map.append(i)
        
        # 提取上下文片段（提取所有位置）
        snippets = []
        for clean_pos in keyword_positions:
            if clean_pos < len(clean_to_original_map) and clean_pos + len(keyword) <= len(clean_to_original_map):
                # 找到原始文本中关键词的开始和结束位置
                keyword_start = clean_to_original_map[clean_pos]
                keyword_end = clean_to_original_map[clean_pos + len(keyword) - 1] + 1
                
                # 在原始文本中提取上下文
                start_pos = max(0, keyword_start - context_length)
                end_pos = min(len(original_text), keyword_end + context_length)
                
                snippet = original_text[start_pos:end_pos]
                
                # 清理snippet中的多余空白
                snippet = re.sub(r'\s+', ' ', snippet).strip()
                
                # 智能高亮关键词
                highlighted_snippet = self.highlight_keyword_in_text(snippet, keyword)
                
                # 添加省略号
                if start_pos > 0:
                    highlighted_snippet = '...' + highlighted_snippet
                if end_pos < len(original_text):
                    highlighted_snippet = highlighted_snippet + '...'
                
                snippets.append(highlighted_snippet)
        
        print(f"    📝 成功提取 {len(snippets)} 个上下文片段")
        return snippets
    
    def highlight_keyword_in_text(self, text: str, keyword: str) -> str:
        """在文本中智能高亮关键词"""
        # 如果关键词长度小于2，使用简单替换
        if len(keyword) < 2:
            return text.replace(keyword, f'<mark class="bg-warning">{keyword}</mark>')
        
        # 先尝试简单匹配（完全相同）
        if keyword in text:
            return text.replace(keyword, f'<mark class="bg-warning">{keyword}</mark>')
        
        # 如果简单匹配失败，使用智能匹配
        keyword_chars = list(keyword)
        pattern_parts = []
        
        for i, char in enumerate(keyword_chars):
            # 转义特殊正则字符
            escaped_char = re.escape(char)
            pattern_parts.append(escaped_char)
            
            # 在字符之间添加可选的非中文字符匹配
            if i < len(keyword_chars) - 1:
                pattern_parts.append(r'[^\u4e00-\u9fa5]{0,3}?')
        
        pattern = ''.join(pattern_parts)
        
        def replace_match(match):
            matched_text = match.group(0)
            return f'<mark class="bg-warning">{matched_text}</mark>'
        
        try:
            highlighted_text = re.sub(pattern, replace_match, text)
            if '<mark class="bg-warning">' in highlighted_text:
                return highlighted_text
            else:
                return text
        except Exception:
            return text
