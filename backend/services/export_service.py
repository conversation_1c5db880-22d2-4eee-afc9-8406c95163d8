"""
导出服务模块
"""
import os
import io
import tempfile
from datetime import datetime
from typing import List, Dict, Any, Optional
from models.database import DatabaseManager
from logger_utils import log_info, log_debug, log_warning, log_error, log_success

# 检查依赖
try:
    import pandas as pd
    from openpyxl.styles import Font, PatternFill, Alignment
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False


class ExportService:
    """导出服务类"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.has_pandas = HAS_PANDAS
    
    def export_analysis_results(self, task_id: str) -> Optional[str]:
        """导出分析结果为Excel"""
        if not self.has_pandas:
            raise ValueError('pandas未安装，无法导出Excel文件')
        
        analysis_results = self.db.get_keyword_analysis(task_id)
        
        if not analysis_results:
            raise ValueError('没有找到分析结果')
        
        # 按照新格式组织数据：公司名称作为行，关键词作为列
        keywords = sorted(list(set(result['keyword'] for result in analysis_results)))
        
        # 按公司分组数据
        company_data = {}
        for result in analysis_results:
            company_name = result['company_name']  # 只使用公司名称作为key
            if company_name not in company_data:
                company_data[company_name] = {}
                # 初始化所有关键词为0
                for keyword in keywords:
                    company_data[company_name][keyword] = 0
            
            # 设置该关键词的出现次数
            company_data[company_name][result['keyword']] = result['count']
        
        # 转换为DataFrame格式
        df_data = []
        for company_name, data in company_data.items():
            row = {'公司名称': company_name}
            # 添加每个关键词的出现次数
            for keyword in keywords:
                row[keyword] = data[keyword]
            df_data.append(row)
        
        # 创建DataFrame
        columns = ['公司名称'] + keywords
        df = pd.DataFrame(df_data, columns=columns)
        
        # 保存Excel文件
        export_dir = 'exports'
        if not os.path.exists(export_dir):
            os.makedirs(export_dir)
        
        filename = f"analysis_results_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        filepath = os.path.join(export_dir, filename)
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='分析结果', index=False)
            
            # 获取工作表对象进行格式化
            worksheet = writer.sheets['分析结果']
            
            # 设置列宽
            worksheet.column_dimensions['A'].width = 30  # 公司名称列更宽
            
            # 设置关键词列宽度
            for i, keyword in enumerate(keywords, start=2):
                col_letter = chr(ord('A') + i - 1)
                if i <= 26:  # 只处理A-Z列
                    worksheet.column_dimensions[col_letter].width = 12
        
        return filepath
    
    def export_filtered_results(self, results_data: List[Dict], filters_info: Dict, 
                               export_format: str = 'excel') -> io.BytesIO:
        """导出筛选后的结果"""
        if not self.has_pandas:
            raise ValueError('pandas未安装，无法导出文件')
        
        if not results_data:
            raise ValueError('没有数据可导出')
        
        log_info(f"导出筛选结果: {len(results_data)} 条记录", "ExportService")
        
        # 创建DataFrame
        df = pd.DataFrame(results_data)
        
        # 确保列的顺序和中文标题
        column_mapping = {
            'stock_code': '股票代码',
            'company_name': '公司名称',
            'file_name': '文件名',
            'keyword': '关键词',
            'count': '出现次数',
            'analysis_date': '分析时间'
        }
        
        # 重新排列列并重命名
        available_columns = [col for col in column_mapping.keys() if col in df.columns]
        df = df[available_columns]
        df = df.rename(columns=column_mapping)
        
        # 格式化分析时间
        if '分析时间' in df.columns:
            df['分析时间'] = pd.to_datetime(df['分析时间']).dt.strftime('%Y-%m-%d %H:%M:%S')
        
        # 生成文件名部分
        filter_parts = []
        if filters_info.get('keywords'):
            filter_parts.append(f"关键词{len(filters_info['keywords'])}个")
        if filters_info.get('companies'):
            filter_parts.append(f"公司{len(filters_info['companies'])}个")
        if filters_info.get('minCount', 0) > 1:
            filter_parts.append(f"最小{filters_info['minCount']}次")
        if filters_info.get('hideZero'):
            filter_parts.append('隐藏0次')
        
        # 根据格式选择导出方式
        if export_format == 'csv':
            # CSV格式导出
            output = io.StringIO()
            df.to_csv(output, index=False, encoding='utf-8-sig')
            output.seek(0)
            
            # 转换为BytesIO
            bytes_output = io.BytesIO()
            bytes_output.write(output.getvalue().encode('utf-8-sig'))
            bytes_output.seek(0)
            
            log_success("筛选结果CSV导出成功", "ExportService")
            return bytes_output
        
        else:
            # Excel格式导出
            output = io.BytesIO()
            
            # 创建Excel文件
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                # 写入主数据
                df.to_excel(writer, sheet_name='筛选结果', index=False)
                
                # 添加筛选信息工作表
                filter_info_data = []
                if filters_info.get('keywords'):
                    filter_info_data.append(['筛选关键词', ', '.join(filters_info['keywords'])])
                if filters_info.get('companies'):
                    filter_info_data.append(['筛选公司', ', '.join(filters_info['companies'])])
                if filters_info.get('minCount', 0) > 1:
                    filter_info_data.append(['最小出现次数', str(filters_info['minCount'])])
                if filters_info.get('hideZero'):
                    filter_info_data.append(['隐藏零次记录', '是'])
                
                filter_info_data.append(['导出时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
                filter_info_data.append(['记录总数', str(len(results_data))])
                
                if filter_info_data:
                    filter_df = pd.DataFrame(filter_info_data, columns=['筛选条件', '值'])
                    filter_df.to_excel(writer, sheet_name='筛选信息', index=False)
                
                # 格式化主工作表
                worksheet = writer.sheets['筛选结果']
                
                # 设置列宽
                column_widths = {
                    'A': 12,  # 股票代码
                    'B': 25,  # 公司名称
                    'C': 35,  # 文件名
                    'D': 15,  # 关键词
                    'E': 12,  # 出现次数
                    'F': 20   # 分析时间
                }
                
                for col, width in column_widths.items():
                    worksheet.column_dimensions[col].width = width
                
                # 设置标题行样式
                title_font = Font(bold=True, color='FFFFFF')
                title_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
                title_alignment = Alignment(horizontal='center', vertical='center')
                
                for cell in worksheet[1]:
                    cell.font = title_font
                    cell.fill = title_fill
                    cell.alignment = title_alignment
            
            output.seek(0)
            log_success("筛选结果Excel导出成功", "ExportService")
            return output
    
    def generate_export_filename(self, filters_info: Dict, export_format: str) -> str:
        """生成导出文件名"""
        filter_parts = []
        if filters_info.get('keywords'):
            filter_parts.append(f"关键词{len(filters_info['keywords'])}个")
        if filters_info.get('companies'):
            filter_parts.append(f"公司{len(filters_info['companies'])}个")
        if filters_info.get('minCount', 0) > 1:
            filter_parts.append(f"最小{filters_info['minCount']}次")
        if filters_info.get('hideZero'):
            filter_parts.append('隐藏0次')
        
        filter_suffix = '_'.join(filter_parts) if filter_parts else '全部'
        timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        extension = 'csv' if export_format == 'csv' else 'xlsx'
        return f"筛选结果_{filter_suffix}_{timestamp_str}.{extension}"
    
    def get_export_mimetype(self, export_format: str) -> str:
        """获取导出文件的MIME类型"""
        if export_format == 'csv':
            return 'text/csv'
        else:
            return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
