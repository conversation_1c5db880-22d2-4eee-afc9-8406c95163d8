"""
应用配置模块
"""
import os

class Config:
    """基础配置类"""

    def __init__(self):
        # Flask配置 - 在实例化时读取环境变量
        self.SECRET_KEY = os.getenv('SECRET_KEY', 'cninfo_spider_secret_key_2024')
        self.FLASK_HOST = os.getenv('FLASK_HOST', '0.0.0.0')
        self.FLASK_PORT = int(os.getenv('FLASK_PORT', '5000'))
        self.FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'

        # 数据库配置
        self.DATABASE_PATH = os.getenv('DATABASE_PATH', 'cninfo_reports.db')

        # OpenAI配置
        self.OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
        self.OPENAI_BASE_URL = os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')
        self.OPENAI_MODEL = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')

        # AI分析配置
        self.CONTEXT_LENGTH = int(os.getenv('CONTEXT_LENGTH', '300'))
        self.MAX_CONTEXTS = int(os.getenv('MAX_CONTEXTS', '10'))
        self.AI_ANALYSIS_TIMEOUT = int(os.getenv('AI_ANALYSIS_TIMEOUT', '120'))

        # 文件路径配置
        self.TXT_DIR = os.getenv('TXT_DIR', 'txt')
        self.PDF_DIR = os.getenv('PDF_DIR', 'pdf')
        self.EXPORTS_DIR = os.getenv('EXPORTS_DIR', 'exports')
        self.RESULTS_DIR = os.getenv('RESULTS_DIR', 'results')

        # 爬虫配置
        self.REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', '10'))
        self.REQUEST_DELAY = float(os.getenv('REQUEST_DELAY', '1.0'))
        self.MAX_RETRIES = int(os.getenv('MAX_RETRIES', '3'))

        # 分页配置
        self.DEFAULT_PAGE_SIZE = int(os.getenv('DEFAULT_PAGE_SIZE', '20'))
        self.MAX_PAGE_SIZE = int(os.getenv('MAX_PAGE_SIZE', '100'))
    
    def get_database_path(self):
        """自动检测环境并返回合适的数据库路径"""
        # 检查是否在Docker容器中
        if os.path.exists('/.dockerenv'):
            # Docker环境
            db_path = 'data/database/cninfo_reports.db'
            print("Docker environment detected")
        else:
            # 本地环境，检查多个可能的路径
            possible_paths = [
                self.DATABASE_PATH,  # 环境变量指定的路径
                'cninfo_reports.db',  # 当前目录
                'database/cninfo_reports.db',  # database子目录
                '../cninfo_reports.db',  # 上级目录
                'data/database/cninfo_reports.db',  # Docker挂载路径
            ]

            db_path = None
            print("Checking database paths...")
            for path in possible_paths:
                exists_text = "exists" if os.path.exists(path) else "not found"
                print("  Checking: {} -> {}".format(path, exists_text))
                if os.path.exists(path):
                    db_path = path
                    print("Local environment, found database: {}".format(path))
                    break

            if not db_path:
                # 如果都没找到，使用默认路径
                db_path = self.DATABASE_PATH
                print("Local environment, using default path: {}".format(db_path))

        return db_path
    
    def ensure_directories(self):
        """确保必要目录存在"""
        directories = [
            'data/database',
            self.TXT_DIR,
            self.PDF_DIR,
            self.EXPORTS_DIR,
            self.RESULTS_DIR,
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def print_config(self):
        """打印配置信息（用于调试）"""
        print("Application Configuration:")
        print("  - Flask Host: {}".format(self.FLASK_HOST))
        print("  - Flask Port: {}".format(self.FLASK_PORT))
        print("  - Flask Debug: {}".format(self.FLASK_DEBUG))
        print("  - Database Path: {}".format(self.get_database_path()))
        api_key_status = "configured" if self.OPENAI_API_KEY else "not configured"
        print("  - OpenAI API Key: {}".format(api_key_status))
        print("  - OpenAI Base URL: {}".format(self.OPENAI_BASE_URL))
        print("  - OpenAI Model: {}".format(self.OPENAI_MODEL))
        print("  - Context Length: {}".format(self.CONTEXT_LENGTH))
        print("  - Max Contexts: {}".format(self.MAX_CONTEXTS))
        print("  - AI Analysis Timeout: {} seconds".format(self.AI_ANALYSIS_TIMEOUT))
        print("  - TXT Directory: {}".format(self.TXT_DIR))
        print("  - PDF Directory: {}".format(self.PDF_DIR))
        print("  - Exports Directory: {}".format(self.EXPORTS_DIR))


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False


class DockerConfig(Config):
    """Docker环境配置"""
    DEBUG = False
    DATABASE_PATH = 'data/database/cninfo_reports.db'


def get_config():
    """根据环境获取配置"""
    env = os.getenv('FLASK_ENV', 'development')

    if env == 'production':
        return ProductionConfig()
    elif os.path.exists('/.dockerenv'):
        return DockerConfig()
    else:
        return DevelopmentConfig()
