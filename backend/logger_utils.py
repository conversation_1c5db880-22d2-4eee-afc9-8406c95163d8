"""
统一日志工具模块
提供规范化的日志输出格式，移除表情图标
"""

import logging
import sys
from datetime import datetime
from typing import Optional


class StandardLogger:
    """标准化日志记录器"""
    
    def __init__(self, name: str = "CNInfo", level: str = "INFO"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # 避免重复添加handler
        if not self.logger.handlers:
            # 创建控制台处理器
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.DEBUG)
            
            # 创建格式化器
            formatter = logging.Formatter(
                '[%(asctime)s] [%(levelname)s] %(name)s: %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            console_handler.setFormatter(formatter)
            
            self.logger.addHandler(console_handler)
    
    def info(self, message: str, module: Optional[str] = None):
        """记录信息日志"""
        if module:
            message = f"{module}: {message}"
        self.logger.info(message)
    
    def debug(self, message: str, module: Optional[str] = None):
        """记录调试日志"""
        if module:
            message = f"{module}: {message}"
        self.logger.debug(message)
    
    def warning(self, message: str, module: Optional[str] = None):
        """记录警告日志"""
        if module:
            message = f"{module}: {message}"
        self.logger.warning(message)
    
    def error(self, message: str, module: Optional[str] = None):
        """记录错误日志"""
        if module:
            message = f"{module}: {message}"
        self.logger.error(message)
    
    def success(self, message: str, module: Optional[str] = None):
        """记录成功日志（使用INFO级别）"""
        if module:
            message = f"{module}: {message}"
        self.logger.info(f"SUCCESS - {message}")


# 创建全局日志实例
logger = StandardLogger()


def log_info(message: str, module: Optional[str] = None):
    """记录信息日志的便捷函数"""
    logger.info(message, module)


def log_debug(message: str, module: Optional[str] = None):
    """记录调试日志的便捷函数"""
    logger.debug(message, module)


def log_warning(message: str, module: Optional[str] = None):
    """记录警告日志的便捷函数"""
    logger.warning(message, module)


def log_error(message: str, module: Optional[str] = None):
    """记录错误日志的便捷函数"""
    logger.error(message, module)


def log_success(message: str, module: Optional[str] = None):
    """记录成功日志的便捷函数"""
    logger.success(message, module)


def simple_print(message: str, level: str = "INFO"):
    """简单的打印函数，用于替换原有的print语句"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] [{level}] {message}")
