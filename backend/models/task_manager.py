"""
任务管理模块
"""
import uuid
import threading
from datetime import datetime
from typing import Dict, Optional, Callable, Any


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.tasks: Dict[str, Dict] = {}
        self.lock = threading.Lock()
    
    def create_task(self, task_name: str, **kwargs) -> str:
        """创建新任务"""
        task_id = str(uuid.uuid4())
        
        with self.lock:
            self.tasks[task_id] = {
                'task_id': task_id,
                'task_name': task_name,
                'status': 'pending',
                'progress': 0,
                'message': '任务创建中...',
                'current_step': 0,
                'total_steps': 0,
                'results': None,
                'start_time': datetime.now().isoformat(),
                'end_time': None,
                'error': None,
                **kwargs
            }
        
        return task_id
    
    def update_task(self, task_id: str, **updates):
        """更新任务状态"""
        with self.lock:
            if task_id in self.tasks:
                self.tasks[task_id].update(updates)
    
    def set_task_running(self, task_id: str, total_steps: int = 0):
        """设置任务为运行状态"""
        self.update_task(task_id, 
                        status='running',
                        total_steps=total_steps,
                        message='任务运行中...')
    
    def update_progress(self, task_id: str, current_step: int, total_steps: int, message: str = ''):
        """更新任务进度"""
        progress = int((current_step / total_steps) * 100) if total_steps > 0 else 0
        self.update_task(task_id,
                        current_step=current_step,
                        total_steps=total_steps,
                        progress=progress,
                        message=message)
    
    def complete_task(self, task_id: str, results: Any = None, message: str = '任务完成'):
        """完成任务"""
        self.update_task(task_id,
                        status='completed',
                        progress=100,
                        message=message,
                        results=results,
                        end_time=datetime.now().isoformat())
    
    def fail_task(self, task_id: str, error: str):
        """任务失败"""
        self.update_task(task_id,
                        status='error',
                        message=f'任务失败: {error}',
                        error=error,
                        end_time=datetime.now().isoformat())
    
    def stop_task(self, task_id: str):
        """停止任务"""
        self.update_task(task_id,
                        status='stopped',
                        message='任务已停止',
                        end_time=datetime.now().isoformat())
    
    def get_task(self, task_id: str) -> Optional[Dict]:
        """获取任务信息"""
        with self.lock:
            return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> Dict[str, Dict]:
        """获取所有任务"""
        with self.lock:
            return self.tasks.copy()
    
    def cleanup_completed_tasks(self, max_tasks: int = 100):
        """清理已完成的任务，保留最近的任务"""
        with self.lock:
            if len(self.tasks) <= max_tasks:
                return
            
            # 按创建时间排序，保留最新的任务
            sorted_tasks = sorted(
                self.tasks.items(),
                key=lambda x: x[1]['start_time'],
                reverse=True
            )
            
            # 保留最新的max_tasks个任务
            tasks_to_keep = dict(sorted_tasks[:max_tasks])
            self.tasks = tasks_to_keep
    
    def run_task_async(self, task_id: str, func: Callable, *args, **kwargs):
        """异步运行任务"""
        def task_wrapper():
            try:
                self.set_task_running(task_id)
                result = func(task_id, *args, **kwargs)
                self.complete_task(task_id, result)
            except Exception as e:
                self.fail_task(task_id, str(e))
        
        thread = threading.Thread(target=task_wrapper)
        thread.daemon = True
        thread.start()
        return thread
